/* ========================================
   ESTILOS DE INTERFAZ DE AUTENTICACIÓN
   Para botones y menús en la barra superior
   ======================================== */

/* Botones de autenticación en la barra superior */
.auth-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.login-btn {
    background: transparent !important;
    border: 1px solid #2e7d32 !important;
    color: #2e7d32 !important;
    width: auto !important;
    padding: 6px 14px !important;
    border-radius: 16px !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    font-family: 'Roboto', sans-serif !important;
}

.login-btn:hover {
    background: #2e7d32 !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(46, 125, 50, 0.2) !important;
}

.register-btn {
    background: #2e7d32 !important;
    color: white !important;
    border: 1px solid #2e7d32 !important;
    width: auto !important;
    padding: 6px 14px !important;
    border-radius: 16px !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    font-family: 'Roboto', sans-serif !important;
}

.register-btn:hover {
    background: #1b5e20 !important;
    border-color: #1b5e20 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(46, 125, 50, 0.3) !important;
}

.login-btn span,
.register-btn span {
    margin-left: 4px;
    font-size: 0.8rem;
}

/* Menú de usuario */
.user-menu {
    position: relative;
}

.user-btn {
    background: #2e7d32 !important;
    color: white !important;
    cursor: pointer;
    width: auto !important;
    padding: 6px 14px !important;
    border-radius: 16px !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    font-family: 'Roboto', sans-serif !important;
    border: 1px solid #2e7d32 !important;
}

.user-btn:hover {
    background: #1b5e20 !important;
    border-color: #1b5e20 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(46, 125, 50, 0.3) !important;
}

.user-btn span {
    margin-left: 4px;
    font-size: 0.8rem;
}

/* Dropdown del usuario */
.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    min-width: 260px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
    margin-top: 8px;
    border: 1px solid #e0e6ed;
    font-family: 'Roboto', sans-serif;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Información del usuario en el dropdown */
.user-info {
    padding: 16px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #2e7d32;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.user-details h4 {
    margin: 0 0 4px 0;
    color: #202124;
    font-size: 0.9rem;
    font-weight: 500;
}

.user-details p {
    margin: 0;
    color: #5f6368;
    font-size: 0.8rem;
}

/* Items del menú de usuario */
.user-menu-items {
    padding: 8px 0;
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 16px;
    color: #202124;
    text-decoration: none;
    transition: background-color 0.15s ease;
    font-size: 0.85rem;
    font-weight: 400;
}

.user-menu-item:hover {
    background: #f8f9fa;
}

.user-menu-item i {
    width: 14px;
    color: #5f6368;
    font-size: 0.8rem;
}

.logout-item {
    color: #d93025 !important;
}

.logout-item:hover {
    background: #fce8e6 !important;
}

.logout-item i {
    color: #d93025 !important;
}

.menu-divider {
    height: 1px;
    background: #f1f3f4;
    margin: 8px 0;
}

/* Responsive para autenticación */
@media (max-width: 768px) {
    .auth-buttons {
        gap: 6px;
    }

    .login-btn,
    .register-btn,
    .user-btn {
        padding: 5px 10px !important;
        font-size: 0.75rem !important;
    }

    .login-btn span,
    .register-btn span,
    .user-btn span {
        display: none;
    }

    .user-dropdown {
        right: -10px;
        min-width: 240px;
    }
}

@media (max-width: 480px) {
    .auth-buttons {
        gap: 4px;
    }

    .login-btn,
    .register-btn,
    .user-btn {
        padding: 4px 8px !important;
        font-size: 0.7rem !important;
        border-radius: 12px !important;
    }

    .user-dropdown {
        right: -15px;
        min-width: 220px;
    }

    .user-info {
        padding: 12px;
    }

    .user-avatar {
        width: 36px;
        height: 36px;
        font-size: 1.1rem;
    }

    .user-details h4 {
        font-size: 0.85rem;
    }

    .user-details p {
        font-size: 0.75rem;
    }

    .user-menu-item {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
}

/* Estados especiales */
.auth-buttons .action-button:focus,
.user-btn:focus {
    outline: 2px solid #2e7d32;
    outline-offset: 2px;
}

/* Animaciones suaves */
.auth-buttons .action-button,
.user-btn {
    will-change: transform;
}

/* Compatibilidad con tema oscuro (preparado para futuro) */
@media (prefers-color-scheme: dark) {
    .user-dropdown {
        background: #2d2d2d;
        border-color: #404040;
    }

    .user-info {
        border-bottom-color: #404040;
    }

    .user-details h4 {
        color: #e8eaed;
    }

    .user-details p {
        color: #9aa0a6;
    }

    .user-menu-item {
        color: #e8eaed;
    }

    .user-menu-item:hover {
        background: #3c4043;
    }

    .user-menu-item i {
        color: #9aa0a6;
    }

    .menu-divider {
        background: #404040;
    }
}
