// Script para manejar las notificaciones
document.addEventListener('DOMContentLoaded', function() {
    // Elementos del DOM
    const notificationBtn = document.getElementById('notificationBtn');
    const notificationDropdown = document.getElementById('notificationDropdown');
    const closeNotificationsBtn = document.getElementById('closeNotifications');

    // Asegurarse de que el botón de notificación tenga posición relativa
    if (notificationBtn) {
        notificationBtn.style.position = 'relative';

        // Asegurarse de que el badge esté visible por defecto
        const badge = notificationBtn.querySelector('.notification-badge');
        if (badge) {
            badge.style.display = 'flex';
        }
    }

    // Función para mostrar/ocultar el dropdown de notificaciones
    function toggleNotifications() {
        if (notificationDropdown.style.display === 'block') {
            // Cerrar el dropdown
            notificationDropdown.style.display = 'none';

            // Mostrar el badge cuando se cierra el dropdown
            const badge = notificationBtn.querySelector('.notification-badge');
            if (badge) {
                badge.style.display = 'flex';
            }
        } else {
            // Abrir el dropdown
            notificationDropdown.style.display = 'block';

            // Ocultar el badge cuando se abre el dropdown
            const badge = notificationBtn.querySelector('.notification-badge');
            if (badge) {
                badge.style.display = 'none';
            }
        }
    }

    // Función para cerrar el dropdown de notificaciones
    function closeNotifications() {
        notificationDropdown.style.display = 'none';

        // Mostrar el badge cuando se cierra el dropdown
        const badge = notificationBtn.querySelector('.notification-badge');
        if (badge) {
            badge.style.display = 'flex';
        }
    }

    // Event listeners
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // Evitar que el click se propague al documento
            toggleNotifications();
        });
    }

    if (closeNotificationsBtn) {
        closeNotificationsBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // Evitar que el click se propague al documento
            closeNotifications();
        });
    }

    // Cerrar el dropdown si se hace clic fuera de él
    document.addEventListener('click', function(e) {
        if (notificationDropdown && notificationDropdown.style.display === 'block') {
            // Verificar si el clic fue dentro del dropdown o en el botón de notificaciones
            if (!notificationDropdown.contains(e.target) && e.target !== notificationBtn && !notificationBtn.contains(e.target)) {
                closeNotifications();

                // Asegurarse de que el badge vuelva a aparecer
                const badge = notificationBtn.querySelector('.notification-badge');
                if (badge) {
                    badge.style.display = 'flex';
                }
            }
        }
    });

    // Prevenir que los clics dentro del dropdown cierren el dropdown
    if (notificationDropdown) {
        notificationDropdown.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Simular notificaciones nuevas (para demostración)
    function simulateNewNotification() {
        // Crear una nueva notificación
        const newNotification = {
            icon: 'fas fa-flask',
            title: 'Nuevo estudio publicado',
            message: 'Se ha publicado un estudio sobre el impacto de la iluminación LED en el crecimiento de pollos de engorde.',
            time: 'Justo ahora'
        };

        // Crear el elemento HTML para la nueva notificación
        const notificationItem = document.createElement('div');
        notificationItem.className = 'notification-item';
        notificationItem.innerHTML = `
            <div class="notification-icon">
                <i class="${newNotification.icon}"></i>
            </div>
            <div class="notification-content">
                <h4 class="notification-title">${newNotification.title}</h4>
                <p class="notification-message">${newNotification.message}</p>
                <span class="notification-time">${newNotification.time}</span>
            </div>
        `;

        // Agregar la nueva notificación al principio de la lista
        const notificationList = document.querySelector('.notification-list');
        if (notificationList) {
            notificationList.insertBefore(notificationItem, notificationList.firstChild);

            // Actualizar el contador de notificaciones
            const badges = document.querySelectorAll('.notification-badge');
            badges.forEach(badge => {
                badge.textContent = parseInt(badge.textContent) + 1;
            });

            // Hacer que la notificación destaque brevemente
            notificationItem.style.backgroundColor = '#e8f5e9';
            setTimeout(() => {
                notificationItem.style.backgroundColor = '';
            }, 3000);
        }
    }

    // Simular una nueva notificación después de 30 segundos (solo para demostración)
    setTimeout(simulateNewNotification, 30000);
});
