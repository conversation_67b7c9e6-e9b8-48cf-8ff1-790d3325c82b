-- ========================================
-- SCRIPT DE CREACIÓN DE TABLAS AVISOFT
-- Base de datos: Avisoftdatabase
-- ========================================

USE Avisoftdatabase;

-- Tabla de usuarios
CREATE TABLE IF NOT EXISTS usuarios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    apellido VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    telefono VARCHAR(20) NULL,
    nombre_granja VARCHAR(255) NULL,
    password_hash VARCHAR(255) NOT NULL,
    fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_ultimo_acceso TIMESTAMP NULL,
    activo BOOLEAN DEFAULT TRUE,
    acepto_terminos BOOLEAN DEFAULT FALSE,
    suscrito_newsletter BOOLEAN DEFAULT FALSE,
    token_verificacion VARCHAR(255) NULL,
    email_verificado BOOLEAN DEFAULT FALSE,
    intentos_login INT DEFAULT 0,
    bloqueado_hasta TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_activo (activo),
    INDEX idx_fecha_registro (fecha_registro)
);

-- Tabla de sesiones de usuario
CREATE TABLE IF NOT EXISTS sesiones_usuario (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    token_sesion VARCHAR(255) NOT NULL UNIQUE,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    recordar_sesion BOOLEAN DEFAULT FALSE,
    fecha_inicio TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_expiracion TIMESTAMP NOT NULL,
    activa BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    INDEX idx_token (token_sesion),
    INDEX idx_usuario (usuario_id),
    INDEX idx_activa (activa),
    INDEX idx_expiracion (fecha_expiracion)
);

-- Tabla de logs de actividad
CREATE TABLE IF NOT EXISTS logs_actividad (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NULL,
    accion VARCHAR(100) NOT NULL,
    descripcion TEXT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    datos_adicionales JSON NULL,
    fecha_accion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE SET NULL,
    INDEX idx_usuario (usuario_id),
    INDEX idx_accion (accion),
    INDEX idx_fecha (fecha_accion)
);

-- Tabla de tokens de recuperación de contraseña
CREATE TABLE IF NOT EXISTS tokens_recuperacion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    usado BOOLEAN DEFAULT FALSE,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_expiracion TIMESTAMP NOT NULL,
    
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_usuario (usuario_id),
    INDEX idx_expiracion (fecha_expiracion)
);

-- Tabla de configuraciones de usuario
CREATE TABLE IF NOT EXISTS configuraciones_usuario (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    clave VARCHAR(100) NOT NULL,
    valor TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_config (usuario_id, clave),
    INDEX idx_usuario (usuario_id),
    INDEX idx_clave (clave)
);

-- Tabla de búsquedas de usuario (para estadísticas)
CREATE TABLE IF NOT EXISTS busquedas_usuario (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NULL,
    termino_busqueda VARCHAR(255) NOT NULL,
    resultados_encontrados INT DEFAULT 0,
    seccion_visitada VARCHAR(100) NULL,
    ip_address VARCHAR(45) NOT NULL,
    fecha_busqueda TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE SET NULL,
    INDEX idx_usuario (usuario_id),
    INDEX idx_termino (termino_busqueda),
    INDEX idx_fecha (fecha_busqueda)
);

-- Insertar usuario administrador por defecto
INSERT INTO usuarios (
    nombre, 
    apellido, 
    email, 
    password_hash, 
    activo, 
    acepto_terminos, 
    email_verificado
) VALUES (
    'Administrador',
    'AviSoft',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
    TRUE,
    TRUE,
    TRUE
) ON DUPLICATE KEY UPDATE email = email;

-- Crear vistas útiles
CREATE OR REPLACE VIEW vista_usuarios_activos AS
SELECT 
    id,
    nombre,
    apellido,
    email,
    telefono,
    nombre_granja,
    fecha_registro,
    fecha_ultimo_acceso,
    suscrito_newsletter
FROM usuarios 
WHERE activo = TRUE;

CREATE OR REPLACE VIEW vista_sesiones_activas AS
SELECT 
    s.id,
    s.usuario_id,
    u.nombre,
    u.apellido,
    u.email,
    s.ip_address,
    s.fecha_inicio,
    s.fecha_expiracion,
    s.recordar_sesion
FROM sesiones_usuario s
JOIN usuarios u ON s.usuario_id = u.id
WHERE s.activa = TRUE 
AND s.fecha_expiracion > NOW();

-- Procedimiento para limpiar sesiones expiradas
DELIMITER //
CREATE PROCEDURE LimpiarSesionesExpiradas()
BEGIN
    UPDATE sesiones_usuario 
    SET activa = FALSE 
    WHERE fecha_expiracion < NOW() AND activa = TRUE;
    
    DELETE FROM tokens_recuperacion 
    WHERE fecha_expiracion < NOW();
END //
DELIMITER ;

-- Evento para limpiar automáticamente sesiones expiradas (cada hora)
CREATE EVENT IF NOT EXISTS limpiar_sesiones_expiradas
ON SCHEDULE EVERY 1 HOUR
DO
    CALL LimpiarSesionesExpiradas();

-- Habilitar el programador de eventos
SET GLOBAL event_scheduler = ON;

-- Mostrar información de las tablas creadas
SELECT 
    TABLE_NAME as 'Tabla',
    TABLE_ROWS as 'Filas',
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as 'Tamaño (MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'Avisoftdatabase'
ORDER BY TABLE_NAME;
