/* ========================================
   ESTILOS PARA LAS SECCIONES PRINCIPALES
   ======================================== */

/* Tarjetas de sección completamente verticales */
.section-card.fully-vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 30px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin-bottom: 30px;
  border-left: none;
  border-top: 5px solid #4caf50;
}

.section-card.fully-vertical:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
}

.section-card.fully-vertical .section-icon-vertical {
  width: 100px;
  height: 100px;
  background-color: #e8f5e9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
  transition: all 0.3s ease;
}

.section-card.fully-vertical:hover .section-icon-vertical {
  background-color: #4caf50;
}

.section-card.fully-vertical:hover .section-icon-vertical i {
  color: white;
}

.section-card.fully-vertical .section-icon-vertical i {
  font-size: 3rem;
  color: #4caf50;
  transition: color 0.3s ease;
}

.section-card.fully-vertical h3 {
  color: #2e7d32;
  margin-bottom: 20px;
  font-size: 1.6rem;
  text-align: center;
  position: relative;
  padding-bottom: 15px;
}

.section-card.fully-vertical h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: #4caf50;
  border-radius: 3px;
}

.section-card.fully-vertical p {
  color: #555;
  line-height: 1.7;
  font-size: 1.15rem;
  margin-bottom: 25px;
  text-align: center;
}

.section-card.fully-vertical .section-features {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

.section-card.fully-vertical .feature-tag {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.section-card.fully-vertical .feature-tag:hover {
  background-color: #4caf50;
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(76, 175, 80, 0.3);
}

/* Estilos para las pestañas de secciones */
.sections-tabs {
  margin: 30px 0;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.tabs-navigation {
  display: flex;
  background-color: #f1f8e9;
  border-bottom: 1px solid #e0e0e0;
}

.tab-button {
  padding: 15px 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
  border-bottom: 3px solid transparent;
}

.tab-button i {
  font-size: 1.2rem;
  color: #4caf50;
  margin-right: 8px;
}

.tab-button.active {
  background-color: white;
  border-bottom: 3px solid #4caf50;
}

.tabs-content {
  background-color: white;
  padding: 20px;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
}

.tab-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.tab-icon {
  font-size: 2rem;
  color: #4caf50;
  margin-right: 15px;
}

.tab-description {
  margin-bottom: 20px;
}

.feature-list {
  list-style: none;
  padding: 0;
}

.feature-list li {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.feature-list li i {
  font-size: 1.2rem;
  color: #4caf50;
  margin-right: 10px;
}

.tab-image img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Estilos para las características de sección */
.section-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.feature-tag {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
}

/* Estilos para el acceso rápido */
.quick-access {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  margin-top: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.quick-access h4 {
  color: #2e7d32;
  margin-bottom: 15px;
  font-size: 1.2rem;
  text-align: center;
}

.quick-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.quick-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 15px 10px;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #555;
  text-decoration: none;
  transition: all 0.3s ease;
}

.quick-link:hover {
  background-color: #e8f5e9;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.quick-link i {
  font-size: 1.5rem;
  color: #4caf50;
  margin-bottom: 8px;
}

/* Estilos para el diseño vertical */
.vertical-layout {
  display: flex;
  flex-direction: column !important;
}

.info-section.vertical {
  display: flex;
  flex-direction: column;
}

.info-box.vertical-layout .info-content,
.benefits-box.vertical-layout .info-content {
  width: 100%;
  max-width: 100%;
}

.info-box.vertical-layout .info-image,
.benefits-box.vertical-layout .info-image {
  width: 100%;
  max-width: 100%;
  margin-top: 30px;
}

.sections-grid.vertical {
  display: flex;
  flex-direction: column;
}

.sections-grid.vertical .section-card {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}

/* Responsive para secciones principales */
@media (max-width: 768px) {
  .tabs-navigation {
    flex-direction: column;
  }
  
  .tab-button {
    justify-content: flex-start;
    padding: 12px 15px;
  }
  
  .quick-links {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .section-card.fully-vertical {
    padding: 20px;
  }
  
  .section-card.fully-vertical .section-icon-vertical {
    width: 80px;
    height: 80px;
  }
  
  .section-card.fully-vertical .section-icon-vertical i {
    font-size: 2.5rem;
  }
  
  .section-card.fully-vertical h3 {
    font-size: 1.4rem;
  }
  
  .section-card.fully-vertical p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .quick-links {
    grid-template-columns: 1fr;
  }
  
  .tab-header {
    flex-direction: column;
    text-align: center;
  }
  
  .tab-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
