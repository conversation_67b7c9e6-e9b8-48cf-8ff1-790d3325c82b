<html lang="es">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>AviSoft</title>
<link rel="stylesheet" type="text/css" href="Styles2.css"/>
</head>
<body>
  <aside>
    <div class="logo">
      <img src="imglogo.png.png" alt="AviSoft Logo">
    </div>
    <nav class="menu">
      <a href="#">Inicio</a>
      <a href="#">Introducción</a>
      <a href="#">Monitoreo</a>
      <a href="#">Settings</a>
      <a href="#">Logout</a>
    </nav>
  </aside>
  <main>
    <div class="header">"Mejora la productividad de tu galpón con soluciones avanzadas"</div>

    <div class="info-section">
      <div class="info-box">
        <h2>Monitorea y mejora la productividad de tu galpón con herramientas automatizadas.</h2>
      </div>
      <div class="benefits-box">
        <h2>Beneficios y Ventajas :</h2>
        <ul>
          <li>Ahorra tiempo y recursos con datos en tiempo real.</li>
          <li>Mejora la salud de tus aves con un control ambiental automatizado.</li>
          <li>Aumenta la productividad de tu galpón con análisis inteligentes</li>
        </ul>
      </div>
    </div>

    <div class="image-section">
      <img src="https://i.imgur.com/V5KHz5w.jpg" alt="Gallinas en galpón">
    </div>

    <div class="cta">
      <h3>¡Que esperas!</h3>
      <button onclick="registrarGalpon()">Registra tu galpón</button>
    </div>

    <table>
      <thead>
        <tr>
          <th>Tipo de ave</th>
          <th>Ubicación</th>
          <th>Fecha - Hora</th>
          <th>U/H/a</th>
          <th>Estado</th>
        </tr>
      </thead>
      <tbody id="tabla-galpon"></tbody>
    </table>

    <div class="monitor-section">
      <h2>Gestión de temperatura y humedad</h2>
      <div class="monitor-row">
        <div class="monitor-label">Sensor</div>
        <div>......</div>
      </div>
      <div class="monitor-row">
        <div class="monitor-label">Estado</div>
        <div>......</div>
      </div>
      <div class="monitor-row">
        <div class="monitor-label">Conexión wi-fi</div>
        <div>......</div>
      </div>
      <div class="monitor-row">
        <div class="monitor-label">Temperatura</div>
        <div>...... °C</div>
        <div class="monitor-label">Humedad</div>
        <div>...... HR</div>
      </div>
      <div class="monitor-row">
        <div class="monitor-label">Fecha</div>
        <div>......</div>
        <div class="monitor-label">Hora</div>
        <div>......</div>
      </div>
    </div>

    <footer>
      &copy;2025 AVISOFT
    </footer>
  </main>