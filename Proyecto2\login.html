<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="css/auth-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <title>Iniciar Sesión - AviSoft</title>
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="auth-card">
            <!-- Logo y título -->
            <div class="auth-header">
                <div class="auth-logo">
                    <img src="assetslogo.png.png" alt="AviSoft Logo">
                </div>
                <h1>Iniciar <PERSON></h1>
                <p>Accede a tu cuenta de AviSoft</p>
            </div>

            <!-- Formulario de login -->
            <form class="auth-form" id="loginForm">
                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i>
                        Correo Electrónico
                    </label>
                    <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                    <span class="error-message" id="emailError"></span>
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        Contraseña
                    </label>
                    <div class="password-input">
                        <input type="password" id="password" name="password" required placeholder="Tu contraseña">
                        <button type="button" class="toggle-password" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <span class="error-message" id="passwordError"></span>
                </div>

                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="rememberMe" name="rememberMe">
                        <span class="checkmark"></span>
                        Recordarme
                    </label>
                    <a href="#" class="forgot-password">¿Olvidaste tu contraseña?</a>
                </div>

                <button type="submit" class="auth-btn primary" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    Iniciar Sesión
                </button>

                <div class="auth-divider">
                    <span>o</span>
                </div>

                <div class="social-login">
                    <button type="button" class="social-btn google">
                        <i class="fab fa-google"></i>
                        Continuar con Google
                    </button>
                    <button type="button" class="social-btn microsoft">
                        <i class="fab fa-microsoft"></i>
                        Continuar con Microsoft
                    </button>
                </div>
            </form>

            <!-- Enlaces adicionales -->
            <div class="auth-footer">
                <p>¿No tienes una cuenta? <a href="registro.html">Regístrate aquí</a></p>
                <p><a href="index.html">← Volver al inicio</a></p>
            </div>
        </div>

        <!-- Panel lateral informativo -->
        <div class="auth-info-panel">
            <div class="info-content">
                <h2>Accede a AviSoft</h2>
                <p>Sistema integral de información avícola para optimizar tu producción y el bienestar de tus aves.</p>

                <div class="info-features">
                    <div class="feature-item">
                        <i class="fas fa-database"></i>
                        <div>
                            <h4>Base de Conocimiento</h4>
                            <p>Acceso a protocolos especializados y guías técnicas actualizadas</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-search"></i>
                        <div>
                            <h4>Búsqueda Inteligente</h4>
                            <p>Encuentra respuestas específicas a tus consultas avícolas</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <div>
                            <h4>Optimización</h4>
                            <p>Mejora la eficiencia y rentabilidad de tu operación</p>
                        </div>
                    </div>
                </div>

                <div class="software-stats">
                    <div class="stat-box">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">Guías Técnicas</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number">4</div>
                        <div class="stat-label">Áreas Especializadas</div>
                    </div>
                </div>

                <div class="access-note">
                    <i class="fas fa-info-circle"></i>
                    <p>Inicia sesión para acceder a contenido personalizado y funciones avanzadas</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Notificación de estado -->
    <div class="notification" id="notification">
        <div class="notification-content">
            <i class="fas fa-check-circle"></i>
            <span id="notificationMessage"></span>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/auth-mysql.js"></script>

    <!-- Script específico para login con MySQL -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');

            if (loginForm) {
                loginForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const formData = new FormData(loginForm);
                    const email = formData.get('email');
                    const password = formData.get('password');
                    const remember = formData.get('remember') === 'on';

                    // Validaciones básicas
                    if (!email || !password) {
                        showNotification('Por favor complete todos los campos', 'error');
                        return;
                    }

                    if (!validateEmail(email)) {
                        showNotification('Por favor ingrese un email válido', 'error');
                        return;
                    }

                    // Intentar login con MySQL
                    const result = await AuthMySQL.login(email, password, remember);

                    if (result.success) {
                        // Redirigir a la página principal después de un breve delay
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1500);
                    }
                });
            }
        });
    </script>
</body>
</html>
