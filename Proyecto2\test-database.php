<?php
// ========================================
// PRUEBA DE CONEXIÓN A BASE DE DATOS
// ========================================

// Headers para mostrar en navegador
header('Content-Type: text/html; charset=utf-8');

// Incluir configuración
require_once __DIR__ . '/config/database.php';

echo "<h1>🔍 Prueba de Conexión a Base de Datos AviSoft</h1>";
echo "<hr>";

// Probar conexión básica
echo "<h2>📡 Prueba de Conexión</h2>";
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        echo "✅ <strong>Conexión exitosa a la base de datos</strong><br>";
        
        // Obtener información de la base de datos
        $info = $database->getDatabaseInfo();
        if ($info) {
            echo "📊 <strong>Base de datos:</strong> " . $info['db_name'] . "<br>";
            echo "🔧 <strong>Versión MySQL:</strong> " . $info['version'] . "<br>";
        }
    } else {
        echo "❌ <strong>Error: No se pudo establecer conexión</strong><br>";
    }
    
} catch (Exception $e) {
    echo "❌ <strong>Error de conexión:</strong> " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Verificar tablas existentes
echo "<h2>📋 Verificación de Tablas</h2>";
try {
    $query = "SHOW TABLES";
    $stmt = $conn->query($query);
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($tables) > 0) {
        echo "✅ <strong>Tablas encontradas (" . count($tables) . "):</strong><br>";
        foreach ($tables as $table) {
            echo "• " . $table . "<br>";
        }
    } else {
        echo "⚠️ <strong>No se encontraron tablas en la base de datos</strong><br>";
        echo "💡 <strong>Sugerencia:</strong> Ejecuta el archivo create_tables.sql<br>";
    }
    
} catch (Exception $e) {
    echo "❌ <strong>Error al verificar tablas:</strong> " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Verificar estructura de tabla usuarios
echo "<h2>👥 Estructura de Tabla 'usuarios'</h2>";
try {
    $query = "DESCRIBE usuarios";
    $stmt = $conn->query($query);
    $columns = $stmt->fetchAll();
    
    if (count($columns) > 0) {
        echo "✅ <strong>Estructura de tabla 'usuarios':</strong><br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>Campo</th>";
        echo "<th style='padding: 8px;'>Tipo</th>";
        echo "<th style='padding: 8px;'>Nulo</th>";
        echo "<th style='padding: 8px;'>Clave</th>";
        echo "<th style='padding: 8px;'>Por Defecto</th>";
        echo "</tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $column['Field'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Type'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Null'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Key'] . "</td>";
            echo "<td style='padding: 8px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Verificar que no existe el campo nombre_granja
        $has_granja_field = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'nombre_granja') {
                $has_granja_field = true;
                break;
            }
        }
        
        if ($has_granja_field) {
            echo "⚠️ <strong>ADVERTENCIA:</strong> El campo 'nombre_granja' aún existe en la tabla<br>";
            echo "💡 <strong>Acción requerida:</strong> Ejecutar: ALTER TABLE usuarios DROP COLUMN nombre_granja;<br>";
        } else {
            echo "✅ <strong>Confirmado:</strong> El campo 'nombre_granja' ha sido eliminado correctamente<br>";
        }
        
    } else {
        echo "❌ <strong>La tabla 'usuarios' no existe</strong><br>";
    }
    
} catch (Exception $e) {
    echo "❌ <strong>Error al verificar estructura:</strong> " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Contar registros en tabla usuarios
echo "<h2>📊 Datos en la Base de Datos</h2>";
try {
    $query = "SELECT COUNT(*) as total FROM usuarios";
    $stmt = $conn->query($query);
    $result = $stmt->fetch();
    
    echo "👥 <strong>Total de usuarios:</strong> " . $result['total'] . "<br>";
    
    if ($result['total'] > 0) {
        $query = "SELECT COUNT(*) as activos FROM usuarios WHERE activo = TRUE";
        $stmt = $conn->query($query);
        $result_activos = $stmt->fetch();
        echo "✅ <strong>Usuarios activos:</strong> " . $result_activos['activos'] . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ <strong>Error al contar usuarios:</strong> " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Probar funciones de configuración
echo "<h2>🔧 Prueba de Funciones</h2>";

// Probar hashPassword
$test_password = "test123";
$hashed = hashPassword($test_password);
echo "🔐 <strong>Hash de contraseña:</strong> " . substr($hashed, 0, 20) . "... ✅<br>";

// Probar verifyPassword
$is_valid = verifyPassword($test_password, $hashed);
echo "🔍 <strong>Verificación de contraseña:</strong> " . ($is_valid ? "✅ Válida" : "❌ Inválida") . "<br>";

// Probar generateSecureToken
$token = generateSecureToken();
echo "🎫 <strong>Token generado:</strong> " . substr($token, 0, 16) . "... ✅<br>";

// Probar validateEmail
$test_email = "<EMAIL>";
$email_valid = validateEmail($test_email);
echo "📧 <strong>Validación de email:</strong> " . ($email_valid ? "✅ Válido" : "❌ Inválido") . "<br>";

echo "<hr>";

// Configuración actual
echo "<h2>⚙️ Configuración Actual</h2>";
echo "🏠 <strong>Host:</strong> " . DB_HOST . "<br>";
echo "🗄️ <strong>Base de datos:</strong> " . DB_NAME . "<br>";
echo "👤 <strong>Usuario:</strong> " . DB_USER . "<br>";
echo "🔒 <strong>Contraseña:</strong> " . (empty(DB_PASS) ? "Sin contraseña" : "Configurada") . "<br>";

echo "<hr>";
echo "<p><strong>✅ Prueba completada</strong></p>";
echo "<p><em>Si ves errores arriba, revisa la configuración en config/database.php</em></p>";
?>
