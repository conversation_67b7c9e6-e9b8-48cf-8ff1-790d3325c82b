@echo off
echo ========================================
echo    SERVIDOR AVISOFT - INICIANDO
echo ========================================
echo.

REM Verificar si PHP está instalado
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP no está instalado en tu sistema
    echo.
    echo 💡 Opciones para instalar PHP:
    echo    1. <PERSON><PERSON><PERSON> desde: https://windows.php.net/download/
    echo    2. Instalar XAMPP: https://www.apachefriends.org/
    echo.
    echo 📋 Después de instalar PHP, ejecuta este archivo nuevamente
    pause
    exit /b 1
)

echo ✅ PHP encontrado:
php --version
echo.

echo 🚀 Iniciando servidor PHP en puerto 8000...
echo 🌐 Accede a: http://localhost:8000
echo.
echo 📋 URLs importantes:
echo    • Registro: http://localhost:8000/registro.html
echo    • Login: http://localhost:8000/login.html
echo    • Inicio: http://localhost:8000/index.html
echo    • Pruebas: http://localhost:8000/test-api.html
echo.
echo ⚠️  Para detener el servidor, presiona Ctrl+C
echo.

REM Iniciar servidor PHP
php -S localhost:8000
