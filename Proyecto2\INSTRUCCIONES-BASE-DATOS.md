# 🗄️ Instrucciones para Configurar la Base de Datos MySQL

## 📋 **Requisitos Previos**

### ✅ **Software Necesario**
- **MySQL Server** (versión 5.7 o superior)
- **MySQL Workbench** (ya instalado)
- **Servidor Web** con PHP (XAMPP, WAMP, LAMP, o similar)
- **PHP** (versión 7.4 o superior)

### ✅ **Extensiones PHP Requeridas**
- `php-mysql` o `php-mysqli`
- `php-pdo`
- `php-json`
- `php-mbstring`

## 🚀 **Pasos de Configuración**

### **Paso 1: Crear la Base de Datos**

1. **Abrir MySQL Workbench**
2. **Conectar a tu servidor MySQL local**
3. **Ejecutar el siguiente comando:**
   ```sql
   CREATE DATABASE IF NOT EXISTS Avisoftdatabase 
   CHARACTER SET utf8mb4 
   COLLATE utf8mb4_unicode_ci;
   ```

### **Paso 2: Crear las Tablas**

1. **Abrir el archivo:** `database/create_tables.sql`
2. **Ejecutar todo el script en MySQL Workbench**
3. **Verificar que se crearon las siguientes tablas:**
   - `usuarios`
   - `sesiones_usuario`
   - `logs_actividad`
   - `tokens_recuperacion`
   - `configuraciones_usuario`
   - `busquedas_usuario`

### **Paso 3: Configurar la Conexión**

1. **Abrir el archivo:** `config/database.php`
2. **Modificar las credenciales según tu configuración:**

```php
// Configuración de la base de datos
define('DB_HOST', 'localhost');        // Tu host MySQL
define('DB_NAME', 'Avisoftdatabase');  // Nombre de la BD
define('DB_USER', 'root');             // Tu usuario MySQL
define('DB_PASS', '');                 // Tu contraseña MySQL
```

### **Paso 4: Configurar el Servidor Web**

#### **Opción A: XAMPP**
1. **Copiar el proyecto** a `C:\xampp\htdocs\avisoft\`
2. **Iniciar Apache y MySQL** desde el panel de XAMPP
3. **Acceder a:** `http://localhost/avisoft/`

#### **Opción B: WAMP**
1. **Copiar el proyecto** a `C:\wamp64\www\avisoft\`
2. **Iniciar WAMP**
3. **Acceder a:** `http://localhost/avisoft/`

#### **Opción C: Servidor Local PHP**
1. **Abrir terminal** en la carpeta del proyecto
2. **Ejecutar:** `php -S localhost:8000`
3. **Acceder a:** `http://localhost:8000/`

### **Paso 5: Verificar la Instalación**

1. **Abrir:** `http://localhost/avisoft/api/verify-session.php`
2. **Deberías ver:** `{"success":false,"message":"Token de sesión no proporcionado"}`
3. **Si ves esto, la API está funcionando correctamente**

## 🔧 **Configuración Avanzada**

### **Configurar Zona Horaria**
```sql
SET GLOBAL time_zone = '-05:00';  -- Para Colombia
```

### **Configurar Variables de Sesión**
```sql
SET GLOBAL event_scheduler = ON;  -- Para limpiar sesiones automáticamente
```

### **Crear Usuario Específico (Recomendado)**
```sql
CREATE USER 'avisoft_user'@'localhost' IDENTIFIED BY 'avisoft_password_2024';
GRANT ALL PRIVILEGES ON Avisoftdatabase.* TO 'avisoft_user'@'localhost';
FLUSH PRIVILEGES;
```

Luego actualizar `config/database.php`:
```php
define('DB_USER', 'avisoft_user');
define('DB_PASS', 'avisoft_password_2024');
```

## 🧪 **Pruebas de Funcionamiento**

### **1. Probar Registro**
```bash
curl -X POST http://localhost/avisoft/api/register.php \
  -H "Content-Type: application/json" \
  -d '{
    "nombre": "Test",
    "apellido": "User",
    "email": "<EMAIL>",
    "password": "Test123456",
    "confirm_password": "Test123456",
    "acepto_terminos": true
  }'
```

### **2. Probar Login**
```bash
curl -X POST http://localhost/avisoft/api/login.php \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123456"
  }'
```

### **3. Verificar Datos en la BD**
```sql
SELECT * FROM usuarios;
SELECT * FROM sesiones_usuario;
SELECT * FROM logs_actividad;
```

## 🔍 **Solución de Problemas**

### **Error: "Connection refused"**
- ✅ Verificar que MySQL esté ejecutándose
- ✅ Verificar host y puerto en `database.php`
- ✅ Verificar credenciales de usuario

### **Error: "Access denied"**
- ✅ Verificar usuario y contraseña en `database.php`
- ✅ Verificar permisos del usuario MySQL
- ✅ Ejecutar `FLUSH PRIVILEGES;`

### **Error: "Database does not exist"**
- ✅ Crear la base de datos: `CREATE DATABASE Avisoftdatabase;`
- ✅ Verificar el nombre en `database.php`

### **Error: "Table doesn't exist"**
- ✅ Ejecutar el script `create_tables.sql`
- ✅ Verificar que todas las tablas se crearon correctamente

### **Error: "500 Internal Server Error"**
- ✅ Verificar logs de PHP: `logs/php_errors.log`
- ✅ Verificar permisos de archivos y carpetas
- ✅ Verificar que PHP tenga las extensiones necesarias

### **Error: "CORS"**
- ✅ Verificar que el archivo `.htaccess` esté en la raíz
- ✅ Verificar que Apache tenga `mod_headers` habilitado
- ✅ Verificar configuración de CORS en las APIs

## 📊 **Estructura de la Base de Datos**

### **Tabla: usuarios**
- `id` - ID único del usuario
- `nombre` - Nombre del usuario
- `apellido` - Apellido del usuario
- `email` - Email único del usuario
- `password_hash` - Contraseña hasheada
- `fecha_registro` - Fecha de registro
- `activo` - Estado del usuario

### **Tabla: sesiones_usuario**
- `id` - ID único de la sesión
- `usuario_id` - ID del usuario
- `token_sesion` - Token único de sesión
- `fecha_expiracion` - Fecha de expiración
- `activa` - Estado de la sesión

### **Tabla: logs_actividad**
- `id` - ID único del log
- `usuario_id` - ID del usuario
- `accion` - Acción realizada
- `fecha_accion` - Fecha de la acción
- `ip_address` - IP del usuario

## 🔐 **Seguridad**

### **Configuraciones Implementadas**
- ✅ **Contraseñas hasheadas** con `password_hash()`
- ✅ **Tokens de sesión seguros** con `random_bytes()`
- ✅ **Validación de entrada** con `filter_var()`
- ✅ **Prepared statements** para prevenir SQL injection
- ✅ **Límite de intentos** de login
- ✅ **Expiración automática** de sesiones

### **Recomendaciones Adicionales**
- 🔒 Usar HTTPS en producción
- 🔒 Configurar firewall para MySQL
- 🔒 Cambiar contraseñas por defecto
- 🔒 Hacer backups regulares
- 🔒 Monitorear logs de actividad

## 📝 **Usuario Administrador por Defecto**

**Email:** `<EMAIL>`  
**Contraseña:** `admin123`

> ⚠️ **Importante:** Cambiar esta contraseña en producción

## 🎯 **Próximos Pasos**

1. ✅ **Configurar la base de datos** siguiendo estos pasos
2. ✅ **Probar el registro** desde la página web
3. ✅ **Probar el login** desde la página web
4. ✅ **Verificar que la sesión** se mantenga entre páginas
5. ✅ **Probar el logout** y verificar que funcione

¡La base de datos MySQL está lista para conectarse con AviSoft! 🚀🗄️✨
