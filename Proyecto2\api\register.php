<?php
// ========================================
// API DE REGISTRO DE USUARIOS AVISOFT
// ========================================

// Headers para CORS y JSON
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Solo permitir método POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

// Incluir dependencias
require_once __DIR__ . '/../classes/User.php';

try {
    // Obtener datos JSON del cuerpo de la petición
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    // Verificar que se recibieron datos
    if (!$data) {
        jsonResponse(['success' => false, 'message' => 'No se recibieron datos válidos'], 400);
    }

    // Validar campos requeridos
    $required_fields = ['nombre', 'apellido', 'email', 'password', 'confirm_password'];
    foreach ($required_fields as $field) {
        if (empty($data[$field])) {
            jsonResponse(['success' => false, 'message' => "El campo '$field' es requerido"], 400);
        }
    }

    // Validaciones adicionales
    if (strlen($data['nombre']) < 2) {
        jsonResponse(['success' => false, 'message' => 'El nombre debe tener al menos 2 caracteres'], 400);
    }

    if (strlen($data['apellido']) < 2) {
        jsonResponse(['success' => false, 'message' => 'El apellido debe tener al menos 2 caracteres'], 400);
    }

    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['success' => false, 'message' => 'El email no tiene un formato válido'], 400);
    }

    if (strlen($data['password']) < 8) {
        jsonResponse(['success' => false, 'message' => 'La contraseña debe tener al menos 8 caracteres'], 400);
    }

    // Validar fortaleza de contraseña
    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $data['password'])) {
        jsonResponse(['success' => false, 'message' => 'La contraseña debe contener al menos una mayúscula, una minúscula y un número'], 400);
    }

    if ($data['password'] !== $data['confirm_password']) {
        jsonResponse(['success' => false, 'message' => 'Las contraseñas no coinciden'], 400);
    }

    // Verificar aceptación de términos
    if (!isset($data['acepto_terminos']) || !$data['acepto_terminos']) {
        jsonResponse(['success' => false, 'message' => 'Debe aceptar los términos y condiciones'], 400);
    }

    // Validar teléfono si se proporciona
    if (!empty($data['telefono'])) {
        // Limpiar teléfono (solo números, espacios, guiones y paréntesis)
        $telefono_limpio = preg_replace('/[^\d\s\-\(\)\+]/', '', $data['telefono']);
        if (strlen($telefono_limpio) < 7) {
            jsonResponse(['success' => false, 'message' => 'El teléfono no tiene un formato válido'], 400);
        }
        $data['telefono'] = $telefono_limpio;
    }

    // Crear instancia de User
    $user = new User();

    // Intentar registrar usuario
    $result = $user->register($data);

    if ($result['success']) {
        // Registro exitoso
        jsonResponse([
            'success' => true,
            'message' => 'Usuario registrado exitosamente',
            'data' => [
                'user_id' => $result['user_id'],
                'email' => $data['email'],
                'nombre' => $data['nombre'],
                'apellido' => $data['apellido']
            ]
        ], 201);
    } else {
        // Error en el registro
        jsonResponse(['success' => false, 'message' => $result['message']], 400);
    }

} catch (PDOException $e) {
    // Error de base de datos
    error_log("Error de BD en registro: " . $e->getMessage());
    
    // Verificar si es error de email duplicado
    if ($e->getCode() == 23000) {
        jsonResponse(['success' => false, 'message' => 'Ya existe una cuenta con este correo electrónico'], 409);
    } else {
        jsonResponse(['success' => false, 'message' => 'Error de base de datos'], 500);
    }

} catch (Exception $e) {
    // Error general
    error_log("Error general en registro: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Error interno del servidor'], 500);
}

/**
 * Función para validar y limpiar datos de entrada
 */
function validateAndCleanData($data) {
    $cleaned = [];
    
    // Limpiar strings
    $string_fields = ['nombre', 'apellido', 'email', 'telefono'];
    foreach ($string_fields as $field) {
        if (isset($data[$field])) {
            $cleaned[$field] = trim(strip_tags($data[$field]));
        }
    }
    
    // Convertir email a minúsculas
    if (isset($cleaned['email'])) {
        $cleaned['email'] = strtolower($cleaned['email']);
    }
    
    // Mantener contraseñas sin modificar
    if (isset($data['password'])) {
        $cleaned['password'] = $data['password'];
    }
    if (isset($data['confirm_password'])) {
        $cleaned['confirm_password'] = $data['confirm_password'];
    }
    
    // Mantener booleanos
    $boolean_fields = ['acepto_terminos', 'suscrito_newsletter'];
    foreach ($boolean_fields as $field) {
        if (isset($data[$field])) {
            $cleaned[$field] = (bool)$data[$field];
        }
    }
    
    return $cleaned;
}

/**
 * Función para registrar intento de registro
 */
function logRegistrationAttempt($email, $success, $error_message = null) {
    try {
        $log_data = [
            'email' => $email,
            'success' => $success,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s'),
            'error' => $error_message
        ];
        
        // En un entorno de producción, esto se guardaría en una tabla de logs
        error_log("Intento de registro: " . json_encode($log_data));
        
    } catch (Exception $e) {
        error_log("Error al registrar intento: " . $e->getMessage());
    }
}

/**
 * Función para enviar email de bienvenida (placeholder)
 */
function sendWelcomeEmail($email, $nombre, $token_verificacion) {
    // En un entorno de producción, aquí se enviaría un email real
    // Por ahora solo registramos en el log
    
    $email_data = [
        'to' => $email,
        'subject' => 'Bienvenido a AviSoft',
        'nombre' => $nombre,
        'verification_token' => $token_verificacion,
        'sent_at' => date('Y-m-d H:i:s')
    ];
    
    error_log("Email de bienvenida (simulado): " . json_encode($email_data));
    
    return true;
}

/**
 * Función para validar fortaleza de contraseña avanzada
 */
function validatePasswordStrength($password) {
    $errors = [];
    
    if (strlen($password) < 8) {
        $errors[] = 'Debe tener al menos 8 caracteres';
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = 'Debe contener al menos una letra minúscula';
    }
    
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = 'Debe contener al menos una letra mayúscula';
    }
    
    if (!preg_match('/\d/', $password)) {
        $errors[] = 'Debe contener al menos un número';
    }
    
    if (preg_match('/(.)\1{2,}/', $password)) {
        $errors[] = 'No debe tener más de 2 caracteres consecutivos iguales';
    }
    
    // Verificar contraseñas comunes
    $common_passwords = ['12345678', 'password', 'qwerty123', 'abc12345'];
    if (in_array(strtolower($password), $common_passwords)) {
        $errors[] = 'No debe usar contraseñas comunes';
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'strength' => calculatePasswordStrength($password)
    ];
}

/**
 * Calcular puntuación de fortaleza de contraseña
 */
function calculatePasswordStrength($password) {
    $score = 0;
    
    // Longitud
    $score += min(strlen($password) * 2, 20);
    
    // Variedad de caracteres
    if (preg_match('/[a-z]/', $password)) $score += 5;
    if (preg_match('/[A-Z]/', $password)) $score += 5;
    if (preg_match('/\d/', $password)) $score += 5;
    if (preg_match('/[^a-zA-Z\d]/', $password)) $score += 10;
    
    // Penalizar patrones
    if (preg_match('/(.)\1{2,}/', $password)) $score -= 10;
    if (preg_match('/123|abc|qwe/i', $password)) $score -= 5;
    
    return min(max($score, 0), 100);
}
?>
