/* ========================================
   ELEMENTOS COMUNES Y GENERALES
   ======================================== */

/* Badge de sección */
.section-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.4rem;
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
  z-index: 10;
}

/* Línea divisoria */
.divider-line {
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #4caf50, #66bb6a);
  margin: 0 auto 30px;
  border-radius: 2px;
}

/* Texto destacado */
.highlight-text {
  font-size: 1.4rem;
  color: #2e7d32;
  font-weight: 600;
  margin-bottom: 20px;
  line-height: 1.6;
  background: linear-gradient(135deg, #f1f8e9, #e8f5e9);
  padding: 20px;
  border-radius: 10px;
  border-left: 5px solid #4caf50;
}

/* Overlay de imagen */
.image-overlay-text {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: 30px;
  color: white;
}

.image-overlay-text h3 {
  font-size: 1.8rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.image-overlay-text p {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

/* Etiquetas generales */
.tag {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
  display: inline-block;
}

.tag:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(76, 175, 80, 0.4);
}

/* Navegación de introducción */
.intro-navigation {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.intro-nav-content h3 {
  color: #2e7d32;
  margin-bottom: 15px;
  font-size: 1.4rem;
  border-bottom: 2px solid #e8f5e9;
  padding-bottom: 10px;
}

.intro-nav-links {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  list-style: none;
  padding: 0;
}

.intro-nav-links li {
  flex: 1;
  min-width: 200px;
}

.intro-nav-links a {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: white;
  border-radius: 8px;
  color: #555;
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid #4caf50;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.intro-nav-links a:hover {
  background-color: #f1f8e9;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.intro-nav-links i {
  font-size: 1.2rem;
  color: #4caf50;
  margin-right: 10px;
}

/* Navegación de página */
.page-navigation {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  margin: 40px 0;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.page-nav-content h4 {
  color: #2e7d32;
  margin-bottom: 15px;
  font-size: 1.3rem;
  text-align: center;
}

.nav-links {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-bottom: 20px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: white;
  border-radius: 8px;
  color: #555;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.nav-link:hover {
  background-color: #f1f8e9;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.nav-link i {
  font-size: 1.1rem;
  color: #4caf50;
  margin-right: 8px;
}

.back-to-top {
  text-align: center;
  margin-top: 20px;
}

.back-to-top a {
  display: inline-flex;
  align-items: center;
  padding: 10px 20px;
  background-color: #4caf50;
  color: white;
  border-radius: 30px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.back-to-top a:hover {
  background-color: #388e3c;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.back-to-top i {
  margin-right: 8px;
}

/* Footer mejorado */
.intro-footer {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 10px 10px 0 0;
  margin-top: 40px;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.footer-logo {
  flex-shrink: 0;
}

.footer-info {
  flex: 1;
}

.footer-info p {
  margin: 5px 0;
  color: #555;
}

.footer-links {
  display: flex;
  gap: 15px;
}

.footer-links a {
  color: #2e7d32;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.footer-links a i {
  margin-right: 5px;
}

.footer-links a:hover {
  text-decoration: underline;
}

/* Animaciones */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

.overlay-point {
  position: absolute;
  width: 30px;
  height: 30px;
  background-color: #4caf50;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

/* Estilos adicionales que estaban en el archivo principal */
.with-badge {
  position: relative;
  padding-top: 40px !important;
}

.divider-line {
  height: 3px;
  width: 60px;
  background-color: #4caf50;
  margin-bottom: 20px;
  border-radius: 3px;
}

.info-tag {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

/* Responsive general */
@media (max-width: 768px) {
  .intro-nav-links {
    flex-direction: column;
  }

  .intro-nav-links li {
    min-width: auto;
  }

  .nav-links {
    flex-direction: column;
    align-items: center;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-links {
    justify-content: center;
  }

  .highlight-text {
    font-size: 1.2rem;
    padding: 15px;
  }

  .section-badge {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .intro-navigation,
  .page-navigation {
    padding: 15px;
  }

  .tag {
    padding: 8px 15px;
    font-size: 0.9rem;
  }

  .image-overlay-text {
    padding: 20px;
  }

  .image-overlay-text h3 {
    font-size: 1.5rem;
  }

  .image-overlay-text p {
    font-size: 1rem;
  }
}
