// ========================================
// SISTEMA DE BÚSQUEDA INTELIGENTE
// ========================================

// Base de datos de búsqueda con palabras clave, preguntas y respuestas
const baseDatosBusqueda = {
    // Control Ambiental
    'control-ambiental': {
        titulo: 'Control Ambiental',
        seccion: 'control-ambiental',
        palabrasClave: [
            // Términos básicos
            'temperatura', 'humedad', 'ventilación', 'calefacción', 'ambiente', 'clima',
            'termómetro', 'calor', 'frío', 'aire', 'corrientes', 'extractores',
            'cama', 'viruta', 'cascarilla', 'amoníaco', 'gases', 'CO2',
            'iluminación', 'luz', 'oscuridad', 'lux', 'bombillas',
            'pollitos', 'pollos', 'aves', 'galpón', 'crianza',
            // Nuevos términos específicos
            'temperatura ideal', 'ventilación del galpón', 'humedad relativa',
            'control de luz', 'calidad del aire', 'ammoniaco en el ambiente',
            'preparación del galpón', 'recepción de pollitos', 'fase de cría',
            'fase de engorde', 'bienestar animal', 'gestión de galpón',
            'manejo de aves', 'control de producción', 'guía de manejo avícola',
            'producción de pollos de engorde', 'cortinas laterales',
            'equipos de calefacción', 'ventiladores', 'paneles evaporativos',
            'lámparas', 'criadoras', 'supervisión diaria'
        ],
        preguntas: [
            '¿Cómo controlar la temperatura del galpón?',
            '¿Qué temperatura necesitan los pollitos?',
            '¿Cómo manejar la humedad en el galpón?',
            '¿Qué hacer si hay mucho amoníaco?',
            '¿Cómo ventilar correctamente?',
            '¿Cuántas horas de luz necesitan?',
            '¿Qué tipo de cama usar?',
            '¿Cómo precalentar el galpón?',
            '¿Qué hacer con las corrientes de aire?',
            '¿Cómo saber si la temperatura es correcta?',
            // Nuevas preguntas específicas
            '¿Cómo recibir pollitos correctamente?',
            '¿Qué temperatura debo mantener?',
            '¿Cómo preparar el galpón antes de recibir pollitos?',
            '¿Cuál es la temperatura ideal para cada fase?',
            '¿Cómo controlar la humedad relativa?',
            '¿Qué hacer con el ammoniaco en el ambiente?'
        ],
        descripcion: 'Guía completa para el manejo ambiental en galpones por fases de crecimiento'
    },

    // Nutrición Especializada
    'nutricion-especializada': {
        titulo: 'Nutrición Especializada',
        seccion: 'nutricion-especializada',
        palabrasClave: [
            // Términos básicos
            'alimentación', 'comida', 'alimento', 'nutrición', 'proteína', 'energía',
            'vitaminas', 'minerales', 'calcio', 'fósforo', 'agua', 'bebederos',
            'comederos', 'pellet', 'migaja', 'concentrado', 'balanceado',
            'conversión', 'consumo', 'peso', 'crecimiento', 'engorde',
            'aminoácidos', 'lisina', 'metionina', 'enzimas', 'probióticos',
            // Nuevos términos específicos
            'plan de alimentación', 'consumo diario de alimento', 'tipos de concentrado',
            'fases de alimentación', 'accesibilidad al agua', 'calidad del agua',
            'comederos y bebederos', 'peso ideal', 'sacrificio', 'faena',
            'calendario de actividades', 'tareas del operario', 'supervisión diaria',
            'manejo de aves', 'control de producción', 'bienestar animal'
        ],
        preguntas: [
            '¿Qué alimento dar a los pollitos?',
            '¿Cuánta proteína necesitan?',
            '¿Cómo calcular la conversión alimenticia?',
            '¿Qué vitaminas son importantes?',
            '¿Cuánta agua deben tomar?',
            '¿Cómo cambiar de alimento?',
            '¿Qué hacer si no comen bien?',
            '¿Cuándo retirar el alimento antes del sacrificio?',
            '¿Qué tipo de comederos usar?',
            '¿Cómo evitar el desperdicio de alimento?',
            // Nuevas preguntas específicas
            '¿Cuánto debe comer un pollo diario?',
            '¿Cuál es el peso ideal a los 35 días?',
            '¿Cómo hacer un plan de alimentación?',
            '¿Qué tipos de concentrado usar en cada fase?',
            '¿Cómo asegurar el acceso al agua?',
            '¿Cuál es la calidad del agua requerida?'
        ],
        descripcion: 'Información sobre alimentación balanceada y programas nutricionales por fases'
    },

    // Salud y Prevención
    'salud-prevencion': {
        titulo: 'Salud y Prevención',
        seccion: 'salud-prevencion',
        palabrasClave: [
            // Términos básicos
            'salud', 'enfermedad', 'vacunación', 'vacunas', 'medicamentos', 'antibióticos',
            'bioseguridad', 'higiene', 'limpieza', 'desinfección', 'mortalidad',
            'síntomas', 'diagnóstico', 'tratamiento', 'prevención', 'veterinario',
            'bronquitis', 'newcastle', 'gumboro', 'coccidiosis', 'estrés',
            'inmunidad', 'defensas', 'cuarentena', 'aislamiento',
            // Nuevos términos específicos
            'bioseguridad avícola', 'programa de vacunación', 'enfermedades comunes',
            'síntomas en aves', 'manejo sanitario', 'medicación preventiva',
            'registro de tratamientos', 'limpieza del galpón', 'desinfección del equipo',
            'manejo de residuos', 'cambio de cama', 'control de vectores',
            'moscas', 'roedores', 'limpieza post-producción', 'bienestar animal',
            'supervisión diaria', 'tareas del operario'
        ],
        preguntas: [
            '¿Qué vacunas aplicar?',
            '¿Cómo prevenir enfermedades?',
            '¿Qué hacer si hay mortalidad alta?',
            '¿Cómo desinfectar el galpón?',
            '¿Cuándo llamar al veterinario?',
            '¿Qué síntomas observar?',
            '¿Cómo aplicar bioseguridad?',
            '¿Qué hacer con aves enfermas?',
            '¿Cómo evitar el estrés?',
            '¿Cuándo usar antibióticos?',
            // Nuevas preguntas específicas
            '¿Cuándo vacunar a los pollos?',
            '¿Qué hacer si hay mortalidad alta?',
            '¿Cómo implementar bioseguridad avícola?',
            '¿Cuáles son las enfermedades comunes?',
            '¿Cómo hacer manejo sanitario?',
            '¿Cómo controlar vectores como moscas y roedores?'
        ],
        descripcion: 'Protocolos de bioseguridad, vacunación y prevención de enfermedades'
    },

    // Diseño de Galpones
    'diseno-galpones': {
        titulo: 'Diseño de Galpones',
        seccion: 'diseno-galpones',
        palabrasClave: [
            // Términos básicos
            'construcción', 'diseño', 'galpón', 'estructura', 'materiales', 'techo',
            'paredes', 'piso', 'drenaje', 'orientación', 'ubicación', 'dimensiones',
            'capacidad', 'densidad', 'espacio', 'metros', 'ventanas', 'puertas',
            'cortinas', 'malla', 'concreto', 'metal', 'madera', 'aislamiento',
            // Nuevos términos específicos
            'gestión de galpón', 'preparación del galpón', 'equipos de calefacción',
            'ventiladores', 'paneles evaporativos', 'cortinas laterales',
            'comederos y bebederos', 'lámparas', 'criadoras', 'instalaciones',
            'infraestructura', 'planificación', 'especificaciones técnicas',
            'bienestar animal', 'control de producción'
        ],
        preguntas: [
            '¿Cómo diseñar un galpón?',
            '¿Qué materiales usar?',
            '¿Cuál es la orientación correcta?',
            '¿Qué dimensiones debe tener?',
            '¿Cuántas aves por metro cuadrado?',
            '¿Cómo hacer el drenaje?',
            '¿Qué tipo de techo usar?',
            '¿Dónde ubicar las ventanas?',
            '¿Cómo calcular la capacidad?',
            '¿Qué altura debe tener?',
            // Nuevas preguntas específicas
            '¿Cómo preparar el galpón para recibir pollitos?',
            '¿Qué equipos necesito en el galpón?',
            '¿Cómo distribuir comederos y bebederos?',
            '¿Dónde colocar las lámparas criadoras?',
            '¿Cómo instalar cortinas laterales?'
        ],
        descripcion: 'Especificaciones técnicas para construcción y diseño óptimo de instalaciones'
    },

    // Bioseguridad
    'bioseguridad': {
        titulo: 'Bioseguridad',
        seccion: 'bioseguridad',
        palabrasClave: [
            // Términos básicos
            'bioseguridad', 'seguridad', 'protocolo', 'higiene', 'limpieza', 'desinfección',
            'visitantes', 'personal', 'ropa', 'calzado', 'pediluvio', 'arco sanitario',
            'cuarentena', 'aislamiento', 'control', 'acceso', 'registro', 'bitácora',
            'plagas', 'roedores', 'insectos', 'aves silvestres', 'contaminación',
            // Nuevos términos específicos
            'bioseguridad avícola', 'limpieza del galpón', 'desinfección del equipo',
            'manejo de residuos', 'control de vectores', 'moscas', 'limpieza post-producción',
            'manejo sanitario', 'protocolos de higiene', 'medidas de seguridad',
            'supervisión diaria', 'tareas del operario', 'calendario de actividades'
        ],
        preguntas: [
            '¿Cómo implementar bioseguridad?',
            '¿Qué protocolos seguir?',
            '¿Cómo controlar visitantes?',
            '¿Qué desinfectantes usar?',
            '¿Cómo hacer un pediluvio?',
            '¿Qué ropa usar en el galpón?',
            '¿Cómo controlar plagas?',
            '¿Qué hacer con aves muertas?',
            '¿Cómo limpiar equipos?',
            '¿Cuándo hacer cuarentena?',
            // Nuevas preguntas específicas
            '¿Cómo implementar bioseguridad avícola?',
            '¿Cómo hacer limpieza post-producción?',
            '¿Cómo controlar vectores como moscas y roedores?',
            '¿Qué protocolos de higiene seguir?',
            '¿Cómo manejar residuos del galpón?'
        ],
        descripcion: 'Medidas de seguridad y protocolos de higiene en la granja avícola'
    },

    // Manejo de Reproductoras
    'manejo-reproductoras': {
        titulo: 'Manejo de Reproductoras',
        seccion: 'manejo-reproductoras',
        palabrasClave: [
            // Términos básicos
            'reproductoras', 'huevos', 'incubación', 'fertilidad', 'eclosión', 'pollitos',
            'gallos', 'gallinas', 'apareamiento', 'nidos', 'ponedoras', 'producción',
            'recolección', 'almacenamiento', 'selección', 'genética', 'línea', 'raza',
            'madurez', 'sexual', 'postura', 'pico', 'producción',
            // Nuevos términos específicos
            'recepción de pollitos', 'manejo de aves', 'bienestar animal',
            'control de producción', 'supervisión diaria', 'tareas del operario',
            'calendario de actividades', 'guía de manejo avícola'
        ],
        preguntas: [
            '¿Cómo manejar reproductoras?',
            '¿Cuándo inicia la postura?',
            '¿Cómo recolectar huevos?',
            '¿Qué hacer con huevos fértiles?',
            '¿Cómo seleccionar reproductores?',
            '¿Cuál es la proporción de gallos?',
            '¿Cómo almacenar huevos?',
            '¿Qué alimentación dar?',
            '¿Cómo mejorar la fertilidad?',
            '¿Cuándo cambiar reproductores?',
            // Nuevas preguntas específicas
            '¿Cómo recibir pollitos correctamente?',
            '¿Cómo hacer el manejo de aves reproductoras?',
            '¿Qué cuidados especiales necesitan?',
            '¿Cómo asegurar el bienestar animal?'
        ],
        descripcion: 'Cuidados especiales para aves reproductoras y producción de huevos fértiles'
    }
};

// Función para normalizar texto (quitar acentos, convertir a minúsculas)
function normalizarTexto(texto) {
    return texto.toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .trim();
}

// Función para buscar coincidencias
function buscarCoincidencias(termino) {
    const terminoNormalizado = normalizarTexto(termino);
    const resultados = [];

    // Si el término es muy corto, no buscar
    if (terminoNormalizado.length < 2) {
        return resultados;
    }

    // Buscar en cada sección
    Object.keys(baseDatosBusqueda).forEach(seccionId => {
        const seccion = baseDatosBusqueda[seccionId];
        let puntuacion = 0;
        let tipoCoincidencia = '';
        let textoCoincidencia = '';

        // Buscar coincidencia exacta en título
        if (normalizarTexto(seccion.titulo).includes(terminoNormalizado)) {
            puntuacion += 100;
            tipoCoincidencia = 'título';
            textoCoincidencia = seccion.titulo;
        }

        // Buscar en palabras clave
        seccion.palabrasClave.forEach(palabra => {
            if (normalizarTexto(palabra).includes(terminoNormalizado)) {
                puntuacion += 50;
                if (!tipoCoincidencia) {
                    tipoCoincidencia = 'palabra clave';
                    textoCoincidencia = palabra;
                }
            }
        });

        // Buscar en preguntas
        seccion.preguntas.forEach(pregunta => {
            if (normalizarTexto(pregunta).includes(terminoNormalizado)) {
                puntuacion += 75;
                if (!tipoCoincidencia || tipoCoincidencia === 'palabra clave') {
                    tipoCoincidencia = 'pregunta';
                    textoCoincidencia = pregunta;
                }
            }
        });

        // Buscar en descripción
        if (normalizarTexto(seccion.descripcion).includes(terminoNormalizado)) {
            puntuacion += 25;
            if (!tipoCoincidencia) {
                tipoCoincidencia = 'descripción';
                textoCoincidencia = seccion.descripcion;
            }
        }

        // Si hay coincidencia, agregar a resultados
        if (puntuacion > 0) {
            resultados.push({
                seccion: seccionId,
                titulo: seccion.titulo,
                descripcion: seccion.descripcion,
                puntuacion: puntuacion,
                tipoCoincidencia: tipoCoincidencia,
                textoCoincidencia: textoCoincidencia
            });
        }
    });

    // Ordenar por puntuación (mayor a menor)
    resultados.sort((a, b) => b.puntuacion - a.puntuacion);

    // Limitar a 6 resultados
    return resultados.slice(0, 6);
}

// Función para mostrar resultados de búsqueda
function mostrarResultados(resultados, termino) {
    const contenedor = document.getElementById('search-results');

    if (!contenedor) {
        // Crear contenedor de resultados si no existe
        crearContenedorResultados();
        return mostrarResultados(resultados, termino);
    }

    // Limpiar resultados anteriores
    contenedor.innerHTML = '';

    if (resultados.length === 0) {
        contenedor.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>No se encontraron resultados para "${termino}"</p>
                <small>Intenta con términos como: temperatura, alimentación, vacunas, bioseguridad</small>
            </div>
        `;
        contenedor.style.display = 'block';
        return;
    }

    // Mostrar resultados
    resultados.forEach(resultado => {
        const elementoResultado = document.createElement('div');
        elementoResultado.className = 'search-result-item';
        elementoResultado.innerHTML = `
            <div class="result-icon">
                <i class="fas ${getIconoSeccion(resultado.seccion)}"></i>
            </div>
            <div class="result-content">
                <h4>${resultado.titulo}</h4>
                <p>${resultado.descripcion}</p>
                <small class="result-type">Encontrado en: ${resultado.tipoCoincidencia}</small>
            </div>
            <div class="result-arrow">
                <i class="fas fa-chevron-right"></i>
            </div>
        `;

        // Agregar evento click para ir a la sección
        elementoResultado.addEventListener('click', () => {
            irASeccion(resultado.seccion);
            ocultarResultados();
        });

        contenedor.appendChild(elementoResultado);
    });

    contenedor.style.display = 'block';
}

// Función para obtener el icono de cada sección
function getIconoSeccion(seccion) {
    const iconos = {
        'control-ambiental': 'fa-temperature-high',
        'nutricion-especializada': 'fa-utensils',
        'salud-prevencion': 'fa-heartbeat',
        'diseno-galpones': 'fa-building',
        'bioseguridad': 'fa-shield-alt',
        'manejo-reproductoras': 'fa-egg'
    };
    return iconos[seccion] || 'fa-lightbulb';
}

// Función para ir a una sección específica
function irASeccion(seccionId) {
    // Si estamos en la página de información
    if (window.location.pathname.includes('Informacion.html') ||
        window.location.pathname.includes('test-')) {

        // Buscar la tarjeta correspondiente
        const tarjeta = document.querySelector(`[data-section="${seccionId}"]`);
        if (tarjeta) {
            // Hacer scroll a la tarjeta
            tarjeta.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // Expandir la tarjeta automáticamente
            setTimeout(() => {
                const botonToggle = tarjeta.querySelector('.toggle-btn');
                if (botonToggle) {
                    botonToggle.click();
                }
            }, 500);

            // Resaltar la tarjeta temporalmente
            tarjeta.style.boxShadow = '0 0 20px rgba(76, 175, 80, 0.5)';
            setTimeout(() => {
                tarjeta.style.boxShadow = '';
            }, 2000);
        }
    } else {
        // Si estamos en otra página, redirigir a la página de información
        window.location.href = `Informacion.html#${seccionId}`;
    }
}

// Función para crear el contenedor de resultados
function crearContenedorResultados() {
    const searchContainer = document.querySelector('.search-container');
    if (!searchContainer) return;

    const contenedor = document.createElement('div');
    contenedor.id = 'search-results';
    contenedor.className = 'search-results-dropdown';
    searchContainer.appendChild(contenedor);
}

// Función para ocultar resultados
function ocultarResultados() {
    const contenedor = document.getElementById('search-results');
    if (contenedor) {
        contenedor.style.display = 'none';
    }
}

// Inicializar búsqueda cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    const inputBusqueda = document.querySelector('.search-container input');

    if (!inputBusqueda) return;

    // Crear contenedor de resultados
    crearContenedorResultados();

    let timeoutBusqueda;

    // Evento de escritura en el input
    inputBusqueda.addEventListener('input', function(e) {
        const termino = e.target.value.trim();

        // Limpiar timeout anterior
        clearTimeout(timeoutBusqueda);

        // Si el campo está vacío, ocultar resultados
        if (termino.length === 0) {
            ocultarResultados();
            return;
        }

        // Buscar después de 300ms de pausa en la escritura
        timeoutBusqueda = setTimeout(() => {
            const resultados = buscarCoincidencias(termino);
            mostrarResultados(resultados, termino);
        }, 300);
    });

    // Ocultar resultados al hacer clic fuera
    document.addEventListener('click', function(e) {
        const searchContainer = document.querySelector('.search-container');
        if (!searchContainer.contains(e.target)) {
            ocultarResultados();
        }
    });

    // Mostrar resultados al hacer clic en el input
    inputBusqueda.addEventListener('focus', function() {
        const termino = this.value.trim();
        if (termino.length > 0) {
            const resultados = buscarCoincidencias(termino);
            mostrarResultados(resultados, termino);
        }
    });
});
