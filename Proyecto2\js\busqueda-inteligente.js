// ========================================
// SISTEMA DE BÚSQUEDA INTELIGENTE
// ========================================

// Base de datos de búsqueda con palabras clave, preguntas y respuestas directas
const baseDatosBusqueda = {
    // Control Ambiental
    'control-ambiental': {
        titulo: 'Control Ambiental',
        seccion: 'control-ambiental',
        palabrasClave: [
            // Términos básicos
            'temperatura', 'humedad', 'ventilación', 'calefacción', 'ambiente', 'clima',
            'termómetro', 'calor', 'frío', 'aire', 'corrientes', 'extractores',
            'cama', 'viruta', 'cascarilla', 'amoníaco', 'gases', 'CO2',
            'iluminación', 'luz', 'oscuridad', 'lux', 'bombillas',
            'pollitos', 'pollos', 'aves', 'galpón', 'crianza',
            // Nuevos términos específicos
            'temperatura ideal', 'ventilación del galpón', 'humedad relativa',
            'control de luz', 'calidad del aire', 'ammoniaco en el ambiente',
            'preparación del galpón', 'recepción de pollitos', 'fase de cría',
            'fase de engorde', 'bienestar animal', 'gestión de galpón',
            'manejo de aves', 'control de producción', 'guía de manejo avícola',
            'producción de pollos de engorde', 'cortinas laterales',
            'equipos de calefacción', 'ventiladores', 'paneles evaporativos',
            'lámparas', 'criadoras', 'supervisión diaria'
        ],
        preguntas: [
            '¿Cómo controlar la temperatura del galpón?',
            '¿Qué temperatura necesitan los pollitos?',
            '¿Cómo manejar la humedad en el galpón?',
            '¿Qué hacer si hay mucho amoníaco?',
            '¿Cómo ventilar correctamente?',
            '¿Cuántas horas de luz necesitan?',
            '¿Qué tipo de cama usar?',
            '¿Cómo precalentar el galpón?',
            '¿Qué hacer con las corrientes de aire?',
            '¿Cómo saber si la temperatura es correcta?',
            // Nuevas preguntas específicas
            '¿Cómo recibir pollitos correctamente?',
            '¿Qué temperatura debo mantener?',
            '¿Cómo preparar el galpón antes de recibir pollitos?',
            '¿Cuál es la temperatura ideal para cada fase?',
            '¿Cómo controlar la humedad relativa?',
            '¿Qué hacer con el ammoniaco en el ambiente?'
        ],
        descripcion: 'Guía completa para el manejo ambiental en galpones por fases de crecimiento'
    },

    // Nutrición Especializada
    'nutricion-especializada': {
        titulo: 'Nutrición Especializada',
        seccion: 'nutricion-especializada',
        palabrasClave: [
            // Términos básicos
            'alimentación', 'comida', 'alimento', 'nutrición', 'proteína', 'energía',
            'vitaminas', 'minerales', 'calcio', 'fósforo', 'agua', 'bebederos',
            'comederos', 'pellet', 'migaja', 'concentrado', 'balanceado',
            'conversión', 'consumo', 'peso', 'crecimiento', 'engorde',
            'aminoácidos', 'lisina', 'metionina', 'enzimas', 'probióticos',
            // Nuevos términos específicos
            'plan de alimentación', 'consumo diario de alimento', 'tipos de concentrado',
            'fases de alimentación', 'accesibilidad al agua', 'calidad del agua',
            'comederos y bebederos', 'peso ideal', 'sacrificio', 'faena',
            'calendario de actividades', 'tareas del operario', 'supervisión diaria',
            'manejo de aves', 'control de producción', 'bienestar animal'
        ],
        preguntas: [
            '¿Qué alimento dar a los pollitos?',
            '¿Cuánta proteína necesitan?',
            '¿Cómo calcular la conversión alimenticia?',
            '¿Qué vitaminas son importantes?',
            '¿Cuánta agua deben tomar?',
            '¿Cómo cambiar de alimento?',
            '¿Qué hacer si no comen bien?',
            '¿Cuándo retirar el alimento antes del sacrificio?',
            '¿Qué tipo de comederos usar?',
            '¿Cómo evitar el desperdicio de alimento?',
            // Nuevas preguntas específicas
            '¿Cuánto debe comer un pollo diario?',
            '¿Cuál es el peso ideal a los 35 días?',
            '¿Cómo hacer un plan de alimentación?',
            '¿Qué tipos de concentrado usar en cada fase?',
            '¿Cómo asegurar el acceso al agua?',
            '¿Cuál es la calidad del agua requerida?'
        ],
        descripcion: 'Información sobre alimentación balanceada y programas nutricionales por fases'
    },

    // Salud y Prevención
    'salud-prevencion': {
        titulo: 'Salud y Prevención',
        seccion: 'salud-prevencion',
        palabrasClave: [
            // Términos básicos
            'salud', 'enfermedad', 'vacunación', 'vacunas', 'medicamentos', 'antibióticos',
            'bioseguridad', 'higiene', 'limpieza', 'desinfección', 'mortalidad',
            'síntomas', 'diagnóstico', 'tratamiento', 'prevención', 'veterinario',
            'bronquitis', 'newcastle', 'gumboro', 'coccidiosis', 'estrés',
            'inmunidad', 'defensas', 'cuarentena', 'aislamiento',
            // Nuevos términos específicos
            'bioseguridad avícola', 'programa de vacunación', 'enfermedades comunes',
            'síntomas en aves', 'manejo sanitario', 'medicación preventiva',
            'registro de tratamientos', 'limpieza del galpón', 'desinfección del equipo',
            'manejo de residuos', 'cambio de cama', 'control de vectores',
            'moscas', 'roedores', 'limpieza post-producción', 'bienestar animal',
            'supervisión diaria', 'tareas del operario'
        ],
        preguntas: [
            '¿Qué vacunas aplicar?',
            '¿Cómo prevenir enfermedades?',
            '¿Qué hacer si hay mortalidad alta?',
            '¿Cómo desinfectar el galpón?',
            '¿Cuándo llamar al veterinario?',
            '¿Qué síntomas observar?',
            '¿Cómo aplicar bioseguridad?',
            '¿Qué hacer con aves enfermas?',
            '¿Cómo evitar el estrés?',
            '¿Cuándo usar antibióticos?',
            // Nuevas preguntas específicas
            '¿Cuándo vacunar a los pollos?',
            '¿Qué hacer si hay mortalidad alta?',
            '¿Cómo implementar bioseguridad avícola?',
            '¿Cuáles son las enfermedades comunes?',
            '¿Cómo hacer manejo sanitario?',
            '¿Cómo controlar vectores como moscas y roedores?'
        ],
        descripcion: 'Protocolos de bioseguridad, vacunación y prevención de enfermedades'
    },

    // Diseño de Galpones
    'diseno-galpones': {
        titulo: 'Diseño de Galpones',
        seccion: 'diseno-galpones',
        palabrasClave: [
            // Términos básicos
            'construcción', 'diseño', 'galpón', 'estructura', 'materiales', 'techo',
            'paredes', 'piso', 'drenaje', 'orientación', 'ubicación', 'dimensiones',
            'capacidad', 'densidad', 'espacio', 'metros', 'ventanas', 'puertas',
            'cortinas', 'malla', 'concreto', 'metal', 'madera', 'aislamiento',
            // Nuevos términos específicos
            'gestión de galpón', 'preparación del galpón', 'equipos de calefacción',
            'ventiladores', 'paneles evaporativos', 'cortinas laterales',
            'comederos y bebederos', 'lámparas', 'criadoras', 'instalaciones',
            'infraestructura', 'planificación', 'especificaciones técnicas',
            'bienestar animal', 'control de producción'
        ],
        preguntas: [
            '¿Cómo diseñar un galpón?',
            '¿Qué materiales usar?',
            '¿Cuál es la orientación correcta?',
            '¿Qué dimensiones debe tener?',
            '¿Cuántas aves por metro cuadrado?',
            '¿Cómo hacer el drenaje?',
            '¿Qué tipo de techo usar?',
            '¿Dónde ubicar las ventanas?',
            '¿Cómo calcular la capacidad?',
            '¿Qué altura debe tener?',
            // Nuevas preguntas específicas
            '¿Cómo preparar el galpón para recibir pollitos?',
            '¿Qué equipos necesito en el galpón?',
            '¿Cómo distribuir comederos y bebederos?',
            '¿Dónde colocar las lámparas criadoras?',
            '¿Cómo instalar cortinas laterales?'
        ],
        descripcion: 'Especificaciones técnicas para construcción y diseño óptimo de instalaciones'
    },

    // Bioseguridad
    'bioseguridad': {
        titulo: 'Bioseguridad',
        seccion: 'bioseguridad',
        palabrasClave: [
            // Términos básicos
            'bioseguridad', 'seguridad', 'protocolo', 'higiene', 'limpieza', 'desinfección',
            'visitantes', 'personal', 'ropa', 'calzado', 'pediluvio', 'arco sanitario',
            'cuarentena', 'aislamiento', 'control', 'acceso', 'registro', 'bitácora',
            'plagas', 'roedores', 'insectos', 'aves silvestres', 'contaminación',
            // Nuevos términos específicos
            'bioseguridad avícola', 'limpieza del galpón', 'desinfección del equipo',
            'manejo de residuos', 'control de vectores', 'moscas', 'limpieza post-producción',
            'manejo sanitario', 'protocolos de higiene', 'medidas de seguridad',
            'supervisión diaria', 'tareas del operario', 'calendario de actividades'
        ],
        preguntas: [
            '¿Cómo implementar bioseguridad?',
            '¿Qué protocolos seguir?',
            '¿Cómo controlar visitantes?',
            '¿Qué desinfectantes usar?',
            '¿Cómo hacer un pediluvio?',
            '¿Qué ropa usar en el galpón?',
            '¿Cómo controlar plagas?',
            '¿Qué hacer con aves muertas?',
            '¿Cómo limpiar equipos?',
            '¿Cuándo hacer cuarentena?',
            // Nuevas preguntas específicas
            '¿Cómo implementar bioseguridad avícola?',
            '¿Cómo hacer limpieza post-producción?',
            '¿Cómo controlar vectores como moscas y roedores?',
            '¿Qué protocolos de higiene seguir?',
            '¿Cómo manejar residuos del galpón?'
        ],
        descripcion: 'Medidas de seguridad y protocolos de higiene en la granja avícola'
    },

    // Manejo de Reproductoras
    'manejo-reproductoras': {
        titulo: 'Manejo de Reproductoras',
        seccion: 'manejo-reproductoras',
        palabrasClave: [
            // Términos básicos
            'reproductoras', 'huevos', 'incubación', 'fertilidad', 'eclosión', 'pollitos',
            'gallos', 'gallinas', 'apareamiento', 'nidos', 'ponedoras', 'producción',
            'recolección', 'almacenamiento', 'selección', 'genética', 'línea', 'raza',
            'madurez', 'sexual', 'postura', 'pico', 'producción',
            // Nuevos términos específicos
            'recepción de pollitos', 'manejo de aves', 'bienestar animal',
            'control de producción', 'supervisión diaria', 'tareas del operario',
            'calendario de actividades', 'guía de manejo avícola'
        ],
        preguntas: [
            '¿Cómo manejar reproductoras?',
            '¿Cuándo inicia la postura?',
            '¿Cómo recolectar huevos?',
            '¿Qué hacer con huevos fértiles?',
            '¿Cómo seleccionar reproductores?',
            '¿Cuál es la proporción de gallos?',
            '¿Cómo almacenar huevos?',
            '¿Qué alimentación dar?',
            '¿Cómo mejorar la fertilidad?',
            '¿Cuándo cambiar reproductores?',
            // Nuevas preguntas específicas
            '¿Cómo recibir pollitos correctamente?',
            '¿Cómo hacer el manejo de aves reproductoras?',
            '¿Qué cuidados especiales necesitan?',
            '¿Cómo asegurar el bienestar animal?'
        ],
        descripcion: 'Cuidados especiales para aves reproductoras y producción de huevos fértiles'
    }
};

// Base de datos de respuestas directas específicas
const respuestasDirectas = {
    // Inicio y preparación
    '¿qué necesito para empezar un galpón de pollos?': {
        respuesta: 'Para empezar un galpón necesitas: 1) Espacio adecuado (mínimo 8-10 pollos por m²), 2) Comederos y bebederos, 3) Sistema de calefacción (lámparas o criadoras), 4) Cama (viruta o cascarilla), 5) Cortinas para control ambiental, 6) Termómetro, 7) Desinfectantes.',
        seccion: 'diseno-galpones'
    },
    '¿cuántos pollos puedo criar en un galpón pequeño?': {
        respuesta: 'En un galpón pequeño de 20m² puedes criar aproximadamente 160-200 pollos de engorde, considerando una densidad de 8-10 aves por metro cuadrado. Para mejores resultados, no excedas esta densidad.',
        seccion: 'diseno-galpones'
    },
    '¿cómo debe estar preparado el galpón antes de que lleguen los pollitos?': {
        respuesta: 'El galpón debe estar: 1) Completamente limpio y desinfectado, 2) Precalentado a 32-35°C, 3) Con cama seca de 5-8cm, 4) Comederos y bebederos instalados, 5) Lámparas funcionando, 6) Cortinas ajustadas, 7) Sin corrientes de aire.',
        seccion: 'control-ambiental'
    },
    '¿qué materiales se usan en el piso del galpón?': {
        respuesta: 'Los mejores materiales para cama son: 1) Viruta de madera (ideal), 2) Cascarilla de arroz, 3) Tamo de trigo, 4) Papel picado. Debe tener 5-8cm de espesor, estar seco y libre de hongos. Evita aserrín muy fino.',
        seccion: 'control-ambiental'
    },
    '¿cómo desinfecto el galpón antes de comenzar?': {
        respuesta: 'Proceso de desinfección: 1) Limpieza seca (retirar toda la cama), 2) Lavado con agua y detergente, 3) Aplicar desinfectante (formol, yodo o amonio cuaternario), 4) Dejar secar 24-48 horas, 5) Colocar cama nueva, 6) Precalentar.',
        seccion: 'bioseguridad'
    },

    // Manejo de pollitos
    '¿cómo se reciben los pollitos recién nacidos?': {
        respuesta: 'Al recibir pollitos: 1) Verificar que el galpón esté a 32-35°C, 2) Colocarlos cerca de la fuente de calor, 3) Ofrecerles agua azucarada las primeras 2 horas, 4) Dar alimento iniciador después de 4-6 horas, 5) Observar que estén activos y no se amontonen.',
        seccion: 'control-ambiental'
    },
    '¿qué temperatura necesitan los pollitos?': {
        respuesta: 'Temperaturas por edad: Semana 1: 32-35°C, Semana 2: 29-32°C, Semana 3: 26-29°C, Semana 4: 23-26°C, Semana 5 en adelante: 20-23°C. Reduce 3°C cada semana gradualmente.',
        seccion: 'control-ambiental'
    },
    '¿cada cuánto deben comer los pollitos?': {
        respuesta: 'Los pollitos deben tener acceso a alimento las 24 horas del día. En las primeras semanas comen cada 2-3 horas. Asegúrate de que los comederos siempre tengan alimento fresco y que todos los pollitos puedan acceder fácilmente.',
        seccion: 'nutricion-especializada'
    },
    '¿cómo sé si los pollitos están cómodos?': {
        respuesta: 'Pollitos cómodos: 1) Se distribuyen uniformemente, 2) Están activos y vocalizan suavemente, 3) Comen y beben regularmente, 4) No se amontonan. Si se agrupan bajo la lámpara: tienen frío. Si se alejan: tienen calor.',
        seccion: 'control-ambiental'
    },
    '¿por qué se agrupan en las esquinas?': {
        respuesta: 'Se agrupan por: 1) Frío (necesitan más calor), 2) Corrientes de aire (revisar cortinas), 3) Miedo o estrés (ruidos fuertes), 4) Falta de espacio en comederos/bebederos. Ajusta la temperatura y elimina corrientes de aire.',
        seccion: 'control-ambiental'
    },

    // Alimentación
    '¿qué tipo de alimento se les da a los pollos?': {
        respuesta: 'Tipos de alimento por edad: 1) Iniciador (0-21 días): 23% proteína, 2) Crecimiento (22-35 días): 20% proteína, 3) Finalizador (36-42 días): 18% proteína. Usa siempre alimento balanceado comercial de buena calidad.',
        seccion: 'nutricion-especializada'
    },
    '¿cuántas veces al día se les da comida?': {
        respuesta: 'Los pollos deben tener alimento disponible las 24 horas. No se alimentan "por veces" sino que comen cuando necesitan. Revisa 2-3 veces al día que los comederos tengan alimento y estén limpios.',
        seccion: 'nutricion-especializada'
    },
    '¿qué cantidad de alimento necesita un pollo?': {
        respuesta: 'Consumo aproximado por edad: Semana 1: 150g total, Semana 2: 450g total, Semana 3: 900g total, Semana 4: 1.5kg total, Semana 5: 2.2kg total, Semana 6: 3kg total. Total ciclo: 3.5-4kg por pollo.',
        seccion: 'nutricion-especializada'
    },
    '¿se les puede dar maíz o restos de comida?': {
        respuesta: 'NO recomendado. Los pollos de engorde necesitan alimento balanceado específico. Maíz solo o restos de comida no tienen los nutrientes necesarios y pueden causar problemas digestivos, crecimiento lento y mayor mortalidad.',
        seccion: 'nutricion-especializada'
    },
    '¿cómo saber si están comiendo bien?': {
        respuesta: 'Señales de buena alimentación: 1) Crecimiento uniforme del lote, 2) Buche lleno al palpar, 3) Pollos activos, 4) Consumo de agua normal (2:1 agua:alimento), 5) Excremento firme, no líquido, 6) Peso según tabla de crecimiento.',
        seccion: 'nutricion-especializada'
    },

    // Agua y ambiente
    '¿qué tipo de agua deben tomar los pollos?': {
        respuesta: 'El agua debe ser: 1) Potable (apta para consumo humano), 2) Fresca y limpia, 3) Sin cloro excesivo, 4) pH entre 6.5-8.5, 5) Libre de bacterias. Si usas agua de pozo, analízala antes. Cambia el agua diariamente.',
        seccion: 'nutricion-especializada'
    },
    '¿cuánta agua necesita un pollo al día?': {
        respuesta: 'Consumo de agua por edad: Semana 1: 50ml/día, Semana 2: 100ml/día, Semana 3: 150ml/día, Semana 4: 200ml/día, Semana 5: 250ml/día, Semana 6: 300ml/día. Proporción general: 2 litros de agua por 1kg de alimento.',
        seccion: 'nutricion-especializada'
    },
    '¿qué pasa si hace mucho calor o mucho frío?': {
        respuesta: 'Mucho calor: pollos jadean, se alejan de la lámpara, beben más agua, comen menos. Solución: aumentar ventilación, bajar temperatura. Mucho frío: se amontonan, tiemblan, comen más. Solución: aumentar calefacción, cerrar cortinas.',
        seccion: 'control-ambiental'
    },
    '¿cuántas horas de luz deben tener al día?': {
        respuesta: 'Programa de luz: Semana 1-2: 24 horas de luz, Semana 3-4: 20-22 horas de luz, Semana 5 en adelante: 18-20 horas de luz. La luz estimula el consumo de alimento y mejora el crecimiento.',
        seccion: 'control-ambiental'
    },
    '¿por qué es importante la ventilación del galpón?': {
        respuesta: 'La ventilación: 1) Elimina gases tóxicos (amoníaco), 2) Controla humedad, 3) Renueva oxígeno, 4) Regula temperatura, 5) Reduce enfermedades respiratorias. Debe ser constante pero sin corrientes directas sobre los pollos.',
        seccion: 'control-ambiental'
    },

    // Salud y enfermedades
    '¿cómo sé si un pollo está enfermo?': {
        respuesta: 'Síntomas de enfermedad: 1) Apatía, no se mueve, 2) No come ni bebe, 3) Plumas erizadas, 4) Ojos cerrados o llorosos, 5) Diarrea, 6) Respiración dificultosa, 7) Se separa del grupo. Aísla inmediatamente y consulta veterinario.',
        seccion: 'salud-prevencion'
    },
    '¿qué hago si encuentro un pollo muerto?': {
        respuesta: 'Al encontrar un pollo muerto: 1) Retíralo inmediatamente del galpón, 2) Usa guantes y bolsa plástica, 3) Desinfecta el área, 4) Entiérralo o quémalo (nunca lo tires a la basura), 5) Registra la muerte, 6) Observa otros pollos por síntomas.',
        seccion: 'salud-prevencion'
    },
    '¿qué vacunas necesitan los pollos?': {
        respuesta: 'Vacunas básicas: 1) Newcastle (día 1 y día 18), 2) Bronquitis infecciosa (día 1), 3) Gumboro (día 14 y 28). Consulta con veterinario local el programa específico para tu zona. Algunas se aplican en el agua, otras inyectadas.',
        seccion: 'salud-prevencion'
    },
    '¿cómo prevenir enfermedades en el galpón?': {
        respuesta: 'Prevención: 1) Bioseguridad estricta (desinfección), 2) Vacunación completa, 3) Agua y alimento de calidad, 4) Ventilación adecuada, 5) Densidad correcta, 6) Limpieza diaria, 7) Control de visitantes, 8) Manejo del estrés.',
        seccion: 'salud-prevencion'
    },
    '¿por qué se les inflama el buche o se les caen?': {
        respuesta: 'Buche inflamado (ingluvitis): por alimento en mal estado, agua contaminada o estrés. Caídas: por debilidad, problemas de patas, densidad alta o pisos resbalosos. Mejora la calidad del alimento/agua y revisa las condiciones del galpón.',
        seccion: 'salud-prevencion'
    },

    // Producción y control
    '¿cuánto tiempo se cría un pollo de engorde?': {
        respuesta: 'Un pollo de engorde se cría entre 35-42 días (5-6 semanas). A los 35 días pesa aproximadamente 2-2.2kg, a los 42 días puede llegar a 2.5-2.8kg. El tiempo depende del peso objetivo y la demanda del mercado.',
        seccion: 'nutricion-especializada'
    },
    '¿cuánto debe pesar un pollo listo para vender?': {
        respuesta: 'Peso ideal para venta: 2.0-2.5kg vivo (35-42 días). Peso canal (sin plumas ni vísceras): 1.4-1.8kg. El peso depende del mercado local: pollos pequeños (1.8-2.2kg) o pollos grandes (2.3-2.8kg).',
        seccion: 'nutricion-especializada'
    },
    '¿cuántos pollos suelen morir por lote?': {
        respuesta: 'Mortalidad normal: 3-5% del lote total. Mortalidad alta (>7%) indica problemas de manejo, enfermedad o estrés. Primera semana: 1-2%, semanas siguientes: 0.5% semanal. Registra diariamente para detectar problemas temprano.',
        seccion: 'salud-prevencion'
    },
    '¿cómo calcular si estoy ganando o perdiendo?': {
        respuesta: 'Cálculo básico: (Precio venta × kg vendidos) - (Costo pollitos + Costo alimento + Costo medicinas + Otros gastos) = Ganancia. Conversión alimenticia ideal: 1.6-1.8 (1.6kg alimento por 1kg de pollo). Registra todos los costos.',
        seccion: 'nutricion-especializada'
    },
    '¿cómo hacer un control diario sin complicaciones?': {
        respuesta: 'Control diario simple: 1) Contar muertos (registrar), 2) Revisar comederos/bebederos, 3) Observar comportamiento general, 4) Verificar temperatura, 5) Limpiar bebederos, 6) Anotar consumo de alimento. Toma 15-20 minutos.',
        seccion: 'control-ambiental'
    },

    // Limpieza y mantenimiento
    '¿cada cuánto debo limpiar el galpón?': {
        respuesta: 'Limpieza diaria: bebederos y comederos. Limpieza semanal: remover cama húmeda. Limpieza total: entre lotes (desinfección completa). Durante producción: mantener cama seca, remover solo áreas muy húmedas o con mucho amoníaco.',
        seccion: 'bioseguridad'
    },
    '¿qué hacer con la cama usada o sucia?': {
        respuesta: 'Cama usada: 1) Compostarla 2-3 meses antes de usar como abono, 2) Venderla como abono orgánico, 3) Mezclarla con cal y dejarla reposar. NUNCA la uses inmediatamente como abono, puede quemar plantas y contener patógenos.',
        seccion: 'bioseguridad'
    },
    '¿cómo evito el mal olor en el galpón?': {
        respuesta: 'Para evitar olores: 1) Ventilación constante, 2) Cama seca (cambiar áreas húmedas), 3) Densidad adecuada, 4) Bebederos sin goteras, 5) Limpiar excrementos acumulados, 6) Usar cal agrícola en cama húmeda (poco).',
        seccion: 'control-ambiental'
    },
    '¿por qué hay muchas moscas o ratones?': {
        respuesta: 'Moscas: por cama húmeda, excrementos, alimento derramado. Ratones: por alimento disponible, refugios. Solución: mantener limpieza, cama seca, almacenar alimento en recipientes cerrados, sellar agujeros, usar trampas.',
        seccion: 'bioseguridad'
    },
    '¿qué productos se usan para desinfectar?': {
        respuesta: 'Desinfectantes comunes: 1) Formol al 2%, 2) Yodo al 1%, 3) Amonio cuaternario, 4) Cal viva, 5) Creolina. Para agua: cloro (2-3 ppm). Siempre limpiar antes de desinfectar. Rotar productos para evitar resistencia.',
        seccion: 'bioseguridad'
    },

    // Preguntas generales
    '¿cuántos pollos necesito para ganar dinero?': {
        respuesta: 'Mínimo rentable: 200-500 pollos por lote. Con 500 pollos y buena gestión puedes ganar $200-400 por ciclo. La rentabilidad depende de: precio del alimento, precio de venta, mortalidad y conversión alimenticia.',
        seccion: 'nutricion-especializada'
    },
    '¿se puede criar sin usar medicamentos?': {
        respuesta: 'Es posible pero más riesgoso. Alternativas: 1) Excelente bioseguridad, 2) Vacunación completa, 3) Probióticos naturales, 4) Ajo, orégano en agua, 5) Manejo perfecto del ambiente. La mortalidad puede ser mayor (5-8%).',
        seccion: 'salud-prevencion'
    },
    '¿qué pasa si se va la luz?': {
        respuesta: 'Sin luz: 1) Los pollos pequeños pueden enfriarse rápidamente, 2) Usar lámparas de gas o velas (con cuidado), 3) Cerrar cortinas para conservar calor, 4) Agrupar pollos en área más cálida, 5) Tener plan de emergencia preparado.',
        seccion: 'control-ambiental'
    },
    '¿qué hago si un pollo no quiere comer?': {
        respuesta: 'Pollo que no come: 1) Revisar si está enfermo (aislarlo), 2) Verificar acceso a comederos, 3) Revisar calidad del alimento, 4) Asegurar agua fresca disponible, 5) Verificar temperatura del galpón, 6) Consultar veterinario si persiste.',
        seccion: 'nutricion-especializada'
    },
    '¿cuál es la mejor raza de pollo para engorde?': {
        respuesta: 'Razas comerciales recomendadas: 1) Cobb 500 (crecimiento rápido), 2) Ross 308 (eficiente conversión), 3) Arbor Acres (resistente), 4) Hubbard (buena carne). Todas son híbridos comerciales, no razas criollas.',
        seccion: 'manejo-reproductoras'
    }
};

// Función para normalizar texto (quitar acentos, convertir a minúsculas)
function normalizarTexto(texto) {
    return texto.toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .trim();
}

// Función para buscar respuestas directas
function buscarRespuestaDirecta(termino) {
    const terminoNormalizado = normalizarTexto(termino);

    // Buscar coincidencia exacta en respuestas directas
    for (const [pregunta, datos] of Object.entries(respuestasDirectas)) {
        const preguntaNormalizada = normalizarTexto(pregunta);
        if (preguntaNormalizada.includes(terminoNormalizado) || terminoNormalizado.includes(preguntaNormalizada.substring(0, 10))) {
            return {
                tipo: 'respuesta_directa',
                pregunta: pregunta,
                respuesta: datos.respuesta,
                seccion: datos.seccion,
                puntuacion: 1000 // Máxima prioridad
            };
        }
    }

    return null;
}

// Función para buscar coincidencias
function buscarCoincidencias(termino) {
    const terminoNormalizado = normalizarTexto(termino);
    const resultados = [];

    // Si el término es muy corto, no buscar
    if (terminoNormalizado.length < 2) {
        return resultados;
    }

    // Primero buscar respuesta directa
    const respuestaDirecta = buscarRespuestaDirecta(termino);
    if (respuestaDirecta) {
        resultados.push(respuestaDirecta);
    }

    // Buscar en cada sección
    Object.keys(baseDatosBusqueda).forEach(seccionId => {
        const seccion = baseDatosBusqueda[seccionId];
        let puntuacion = 0;
        let tipoCoincidencia = '';
        let textoCoincidencia = '';

        // Buscar coincidencia exacta en título
        if (normalizarTexto(seccion.titulo).includes(terminoNormalizado)) {
            puntuacion += 100;
            tipoCoincidencia = 'título';
            textoCoincidencia = seccion.titulo;
        }

        // Buscar en palabras clave
        seccion.palabrasClave.forEach(palabra => {
            if (normalizarTexto(palabra).includes(terminoNormalizado)) {
                puntuacion += 50;
                if (!tipoCoincidencia) {
                    tipoCoincidencia = 'palabra clave';
                    textoCoincidencia = palabra;
                }
            }
        });

        // Buscar en preguntas
        seccion.preguntas.forEach(pregunta => {
            if (normalizarTexto(pregunta).includes(terminoNormalizado)) {
                puntuacion += 75;
                if (!tipoCoincidencia || tipoCoincidencia === 'palabra clave') {
                    tipoCoincidencia = 'pregunta';
                    textoCoincidencia = pregunta;
                }
            }
        });

        // Buscar en descripción
        if (normalizarTexto(seccion.descripcion).includes(terminoNormalizado)) {
            puntuacion += 25;
            if (!tipoCoincidencia) {
                tipoCoincidencia = 'descripción';
                textoCoincidencia = seccion.descripcion;
            }
        }

        // Si hay coincidencia, agregar a resultados
        if (puntuacion > 0) {
            resultados.push({
                seccion: seccionId,
                titulo: seccion.titulo,
                descripcion: seccion.descripcion,
                puntuacion: puntuacion,
                tipoCoincidencia: tipoCoincidencia,
                textoCoincidencia: textoCoincidencia
            });
        }
    });

    // Ordenar por puntuación (mayor a menor)
    resultados.sort((a, b) => b.puntuacion - a.puntuacion);

    // Limitar a 6 resultados
    return resultados.slice(0, 6);
}

// Función para mostrar resultados de búsqueda
function mostrarResultados(resultados, termino) {
    const contenedor = document.getElementById('searchResults') ||
                      document.getElementById('search-results');

    if (!contenedor) {
        // Crear contenedor de resultados si no existe
        crearContenedorResultados();
        return mostrarResultados(resultados, termino);
    }

    // Limpiar resultados anteriores
    contenedor.innerHTML = '';

    if (resultados.length === 0) {
        contenedor.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>No se encontraron resultados para "${termino}"</p>
                <small>Intenta con términos como: temperatura, alimentación, vacunas, bioseguridad</small>
            </div>
        `;
        contenedor.style.display = 'block';
        return;
    }

    // Mostrar resultados
    resultados.forEach(resultado => {
        const elementoResultado = document.createElement('div');

        if (resultado.tipo === 'respuesta_directa') {
            // Mostrar respuesta directa
            elementoResultado.className = 'search-result-item direct-answer';
            elementoResultado.innerHTML = `
                <div class="result-content">
                    <h4 class="direct-question">${resultado.pregunta}</h4>
                    <p class="direct-response">${resultado.respuesta}</p>
                </div>
            `;

            // Agregar evento click para ir a la sección
            elementoResultado.addEventListener('click', () => {
                irASeccion(resultado.seccion);
                ocultarResultados();
            });
        } else {
            // Mostrar resultado normal
            elementoResultado.className = 'search-result-item';
            elementoResultado.innerHTML = `
                <div class="result-content">
                    <h4>${resultado.titulo}</h4>
                    <p>${resultado.descripcion}</p>
                    <small class="result-type">${resultado.tipoCoincidencia}</small>
                </div>
            `;

            // Agregar evento click para ir a la sección
            elementoResultado.addEventListener('click', () => {
                irASeccion(resultado.seccion);
                ocultarResultados();
            });
        }

        contenedor.appendChild(elementoResultado);
    });

    contenedor.style.display = 'block';
}

// Función para obtener el icono de cada sección
function getIconoSeccion(seccion) {
    const iconos = {
        'control-ambiental': 'fa-temperature-high',
        'nutricion-especializada': 'fa-utensils',
        'salud-prevencion': 'fa-heartbeat',
        'diseno-galpones': 'fa-building',
        'bioseguridad': 'fa-shield-alt',
        'manejo-reproductoras': 'fa-egg'
    };
    return iconos[seccion] || 'fa-lightbulb';
}

// Función para ir a una sección específica
function irASeccion(seccionId) {
    // Si estamos en la página de información
    if (window.location.pathname.includes('Informacion.html') ||
        window.location.pathname.includes('test-')) {

        // Buscar la tarjeta correspondiente
        const tarjeta = document.querySelector(`[data-section="${seccionId}"]`);
        if (tarjeta) {
            // Hacer scroll a la tarjeta
            tarjeta.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // Expandir la tarjeta automáticamente
            setTimeout(() => {
                const botonToggle = tarjeta.querySelector('.toggle-btn');
                if (botonToggle) {
                    botonToggle.click();
                }
            }, 500);

            // Resaltar la tarjeta temporalmente
            tarjeta.style.boxShadow = '0 0 20px rgba(76, 175, 80, 0.5)';
            setTimeout(() => {
                tarjeta.style.boxShadow = '';
            }, 2000);
        }
    } else {
        // Si estamos en otra página, redirigir a la página de información
        window.location.href = `Informacion.html#${seccionId}`;
    }
}

// Función para crear el contenedor de resultados
function crearContenedorResultados() {
    const searchContainer = document.querySelector('.search-container');
    if (!searchContainer) return;

    // Verificar si ya existe un contenedor de resultados
    let contenedor = document.getElementById('searchResults') ||
                    document.getElementById('search-results');

    if (!contenedor) {
        contenedor = document.createElement('div');
        contenedor.id = 'search-results';
        contenedor.className = 'search-results-dropdown';
        contenedor.style.display = 'none';
        searchContainer.appendChild(contenedor);
    }
}

// Función para ocultar resultados
function ocultarResultados() {
    const contenedor = document.getElementById('searchResults') ||
                      document.getElementById('search-results');
    if (contenedor) {
        contenedor.style.display = 'none';
    }
}

// Inicializar búsqueda cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Buscar el input de búsqueda por ID o por selector
    const inputBusqueda = document.getElementById('searchInput') ||
                         document.querySelector('.search-container input');

    if (!inputBusqueda) return;

    // Crear contenedor de resultados
    crearContenedorResultados();

    let timeoutBusqueda;

    // Evento de escritura en el input
    inputBusqueda.addEventListener('input', function(e) {
        const termino = e.target.value.trim();

        // Limpiar timeout anterior
        clearTimeout(timeoutBusqueda);

        // Si el campo está vacío, ocultar resultados
        if (termino.length === 0) {
            ocultarResultados();
            return;
        }

        // Buscar después de 300ms de pausa en la escritura
        timeoutBusqueda = setTimeout(() => {
            const resultados = buscarCoincidencias(termino);
            mostrarResultados(resultados, termino);
        }, 300);
    });

    // Ocultar resultados al hacer clic fuera
    document.addEventListener('click', function(e) {
        const searchContainer = document.querySelector('.search-container');
        if (!searchContainer.contains(e.target)) {
            ocultarResultados();
        }
    });

    // Mostrar resultados al hacer clic en el input
    inputBusqueda.addEventListener('focus', function() {
        const termino = this.value.trim();
        if (termino.length > 0) {
            const resultados = buscarCoincidencias(termino);
            mostrarResultados(resultados, termino);
        }
    });
});
