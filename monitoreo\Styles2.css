
    * {
    box-sizing: border-box;
    font-family: 'Roboto', sans-serif;
    margin: 0;
    padding: 0;
    }
    body {
    display: flex;
    min-height: 100vh;
    background: #fff;
    }
    aside {
    width: 220px;
    background: #f3fdf1;
    border-right: 2px solid #d3f0c2;
    padding: 20px;
    }
    .logo img {
    max-width: 100%;
    height: auto;
    }
    .menu a {
    display: block;
    margin: 10px 0;
    padding: 10px;
    background: #e0f7d9;
    text-decoration: none;
    color: #000;
    border-radius: 6px;
    }
    .menu a:hover {
    background: #c8f0bb;
    }
    main {
    flex: 1;
    padding: 30px;
    }
    .header {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 20px;
    }
    .info-section {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    }
    .info-box, .benefits-box {
    flex: 1;
    padding: 20px;
    border: 2px solid #62b420;
    border-radius: 12px;
    }
    .info-box h2, .benefits-box h2 {
    color: #2b9a06;
    font-size: 18px;
    margin-bottom: 10px;
    }
    .info-box p, .benefits-box ul {
    font-size: 16px;
    font-weight: bold;
    }
    .benefits-box ul {
    list-style-type: '✅';
    padding-left: 20px;
    }
    .image-section {
    margin: 20px 0;
    text-align: center;
    }
    .image-section img {
    max-width: 100%;
    border-radius: 16px;
    border: 4px solid #d0d0d0;
    }
    .cta {
    text-align: center;
    margin: 30px 0;
    }
    .cta button {
    background: #41bd18;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    }
    .cta button:hover {
    background: #319a11;
    }
    footer {
    text-align: center;
    padding: 10px;
    color: gray;
    }
    table {
    width: 100%;
    margin-top: 20px;
    border-collapse: collapse;
    }
    th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
    }
    th {
    background-color: #e6f4e6;
    color: #000;
    }

    .monitor-section {
    margin-top: 40px;
    border: 2px solid green;
    padding: 20px;
    border-radius: 10px;
    }
    .monitor-section h2 {
    background: #f0f0f0;
    padding: 10px;
    color: green;
    margin-bottom: 20px;
    }
    .monitor-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    }
    .monitor-row {
    display: flex;
    justify-content: space-between;
    border: 2px solid green;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 10px;
    }
    .monitor-label {
    font-weight: bold;
    }
