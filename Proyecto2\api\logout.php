<?php
// ========================================
// API DE LOGOUT DE USUARIOS AVISOFT
// ========================================

// Headers para CORS y JSON
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Solo permitir método POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

// Incluir dependencias
require_once __DIR__ . '/../classes/User.php';

try {
    // Obtener token de sesión
    $token = null;
    
    // Verificar en headers Authorization
    $headers = getallheaders();
    if (isset($headers['Authorization'])) {
        $auth_header = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $token = $matches[1];
        }
    }
    
    // Verificar en POST data
    if (!$token) {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        if (isset($data['token'])) {
            $token = $data['token'];
        }
    }
    
    // Verificar en cookies
    if (!$token && isset($_COOKIE['avisoft_session'])) {
        $token = $_COOKIE['avisoft_session'];
    }

    if (!$token) {
        jsonResponse(['success' => false, 'message' => 'Token de sesión no proporcionado'], 400);
    }

    // Crear instancia de User
    $user = new User();

    // Verificar que la sesión existe antes de cerrarla
    $session = $user->verifySession($token);
    
    if ($session) {
        // Cerrar sesión
        $logout_result = $user->logout($token);
        
        if ($logout_result) {
            // Limpiar cookie si existe
            if (isset($_COOKIE['avisoft_session'])) {
                setcookie('avisoft_session', '', time() - 3600, '/');
            }
            
            // Registrar logout en logs
            logLogoutActivity($session['user_id'], $session['email']);
            
            jsonResponse([
                'success' => true,
                'message' => 'Sesión cerrada exitosamente'
            ], 200);
            
        } else {
            jsonResponse(['success' => false, 'message' => 'Error al cerrar sesión'], 500);
        }
        
    } else {
        // La sesión ya no existe o es inválida
        jsonResponse([
            'success' => true,
            'message' => 'Sesión ya cerrada o inválida'
        ], 200);
    }

} catch (Exception $e) {
    error_log("Error en logout: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Error interno del servidor'], 500);
}

/**
 * Función para registrar actividad de logout
 */
function logLogoutActivity($user_id, $email) {
    try {
        $log_data = [
            'user_id' => $user_id,
            'email' => $email,
            'action' => 'logout',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        error_log("Logout exitoso: " . json_encode($log_data));
        
    } catch (Exception $e) {
        error_log("Error al registrar logout: " . $e->getMessage());
    }
}
?>
