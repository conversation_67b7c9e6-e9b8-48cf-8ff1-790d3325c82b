/* ========================================
   ESTILOS PARA PESTAÑA DE PERFIL
   ======================================== */

/* Contenedor principal de la pestaña de perfil */
.profile-tab {
    position: fixed;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    z-index: 1000;
    font-family: 'Roboto', sans-serif;
    transition: all 0.3s ease;
}

/* Header de la pestaña */
.profile-tab-header {
    background: #2e7d32;
    color: white;
    padding: 12px 8px;
    border-radius: 8px 0 0 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    min-width: 50px;
    transition: all 0.3s ease;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

.profile-tab-header:hover {
    background: #1b5e20;
    transform: translateX(-5px);
}

.profile-tab-header i {
    font-size: 1.2rem;
}

.profile-tab-header span {
    font-size: 0.8rem;
    font-weight: 500;
    writing-mode: vertical-rl;
    text-orientation: mixed;
}

.profile-tab-arrow {
    font-size: 0.7rem !important;
    transition: transform 0.3s ease;
}

.profile-tab-arrow.rotated {
    transform: rotate(180deg);
}

/* Contenido de la pestaña */
.profile-tab-content {
    position: absolute;
    top: 0;
    right: 100%;
    width: 320px;
    background: white;
    border-radius: 8px 0 0 8px;
    box-shadow: -4px 0 16px rgba(0, 0, 0, 0.15);
    border: 1px solid #e0e6ed;
    border-right: none;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateX(100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.profile-tab-content.open {
    transform: translateX(0);
    opacity: 1;
    visibility: visible;
}

/* Estado no logueado */
.profile-not-logged {
    padding: 24px;
    text-align: center;
}

.profile-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.profile-empty-state i {
    font-size: 3rem;
    color: #9e9e9e;
    margin-bottom: 8px;
}

.profile-empty-state h4 {
    margin: 0;
    color: #424242;
    font-size: 1.1rem;
    font-weight: 500;
}

.profile-empty-state p {
    margin: 0;
    color: #757575;
    font-size: 0.9rem;
    line-height: 1.4;
}

.profile-auth-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    margin-top: 8px;
}

/* Estado logueado */
.profile-logged {
    padding: 20px;
}

.profile-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.profile-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #2e7d32;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
}

.profile-details h3 {
    margin: 0 0 4px 0;
    color: #212121;
    font-size: 1rem;
    font-weight: 600;
}

.profile-details p {
    margin: 0 0 6px 0;
    color: #757575;
    font-size: 0.85rem;
}

.profile-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.75rem;
    color: #4caf50;
    font-weight: 500;
}

.profile-status i {
    font-size: 0.6rem;
}

/* Grid de información */
.profile-info-grid {
    display: grid;
    gap: 12px;
    margin-bottom: 20px;
}

.profile-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
}

.profile-info-item:last-child {
    border-bottom: none;
}

.profile-info-item label {
    font-size: 0.8rem;
    color: #616161;
    font-weight: 500;
}

.profile-info-item span {
    font-size: 0.8rem;
    color: #212121;
    text-align: right;
    max-width: 60%;
    word-break: break-word;
}

/* Botones de la pestaña de perfil */
.profile-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-family: inherit;
}

.profile-btn.login {
    background: transparent;
    color: #2e7d32;
    border: 1px solid #2e7d32;
}

.profile-btn.login:hover {
    background: #2e7d32;
    color: white;
}

.profile-btn.register {
    background: #2e7d32;
    color: white;
    border: 1px solid #2e7d32;
}

.profile-btn.register:hover {
    background: #1b5e20;
    border-color: #1b5e20;
}

.profile-btn.edit {
    background: #f5f5f5;
    color: #424242;
    border: 1px solid #e0e0e0;
}

.profile-btn.edit:hover {
    background: #eeeeee;
    border-color: #d0d0d0;
}

.profile-btn.logout {
    background: #ffebee;
    color: #d32f2f;
    border: 1px solid #ffcdd2;
}

.profile-btn.logout:hover {
    background: #ffcdd2;
    border-color: #ef9a9a;
}

/* Acciones del perfil */
.profile-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
}

/* Responsive */
@media (max-width: 768px) {
    .profile-tab-content {
        width: 280px;
    }
    
    .profile-tab-header {
        padding: 10px 6px;
        min-width: 45px;
    }
    
    .profile-tab-header i {
        font-size: 1.1rem;
    }
    
    .profile-tab-header span {
        font-size: 0.75rem;
    }
    
    .profile-logged {
        padding: 16px;
    }
    
    .profile-user-info {
        gap: 10px;
    }
    
    .profile-avatar {
        width: 45px;
        height: 45px;
        font-size: 1.6rem;
    }
    
    .profile-details h3 {
        font-size: 0.95rem;
    }
    
    .profile-details p {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .profile-tab-content {
        width: 260px;
    }
    
    .profile-tab-header {
        padding: 8px 5px;
        min-width: 40px;
    }
    
    .profile-tab-header i {
        font-size: 1rem;
    }
    
    .profile-tab-header span {
        font-size: 0.7rem;
    }
    
    .profile-not-logged {
        padding: 20px;
    }
    
    .profile-logged {
        padding: 14px;
    }
    
    .profile-empty-state i {
        font-size: 2.5rem;
    }
    
    .profile-empty-state h4 {
        font-size: 1rem;
    }
    
    .profile-empty-state p {
        font-size: 0.85rem;
    }
}

/* Animaciones */
@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutToRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.profile-tab-content.opening {
    animation: slideInFromRight 0.3s ease forwards;
}

.profile-tab-content.closing {
    animation: slideOutToRight 0.3s ease forwards;
}

/* Scrollbar personalizada */
.profile-tab-content::-webkit-scrollbar {
    width: 4px;
}

.profile-tab-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.profile-tab-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.profile-tab-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
