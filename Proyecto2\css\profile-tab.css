/* ========================================
   ESTILOS PARA PESTAÑA DE PERFIL ESTILO FACEBOOK
   ======================================== */

/* Contenedor principal de la pestaña de perfil integrada */
.profile-tab-topbar {
    position: relative;
    display: inline-block;
    font-family: 'Roboto', sans-serif;
}

/* Botón de perfil estilo Facebook - solo visible cuando está logueado */
.profile-btn-header {
    background: transparent;
    color: #1c1e21;
    padding: 6px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all 0.2s ease;
    border: none;
    width: 40px;
    height: 40px;
    position: relative;
    overflow: hidden;
}

/* Avatar circular del usuario */
.profile-btn-header .profile-avatar-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2e7d32, #4caf50);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-btn-header:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: none;
}

.profile-btn-header:hover .profile-avatar-btn {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Ocultar texto y flecha en el nuevo diseño */
.profile-btn-header span,
.profile-arrow {
    display: none;
}

/* Estado no logueado - botón oculto */
.profile-tab-topbar.not-logged {
    display: none;
}

/* Dropdown estilo Facebook */
.profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 360px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.05);
    border: none;
    max-height: 80vh;
    overflow: hidden;
    transform: translateY(-10px) scale(0.95);
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s cubic-bezier(0.2, 0, 0, 1);
    z-index: 1000;
    margin-top: 12px;
}

.profile-dropdown.open {
    transform: translateY(0) scale(1);
    opacity: 1;
    visibility: visible;
}

/* Flecha del dropdown */
.profile-dropdown::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 12px;
    width: 16px;
    height: 16px;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
    z-index: 1001;
}

/* Header del dropdown estilo Facebook */
.profile-dropdown-header {
    padding: 16px 20px 12px;
    border-bottom: 1px solid #e4e6ea;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.profile-dropdown-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1c1e21;
}

/* Contenido principal del dropdown */
.profile-logged {
    padding: 0;
    overflow-y: auto;
    max-height: 400px;
}

/* Información del usuario estilo Facebook */
.profile-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e6ea;
    background: white;
    transition: background-color 0.2s ease;
}

.profile-user-info:hover {
    background: #f8f9fa;
}

.profile-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2e7d32, #4caf50);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.profile-details {
    flex: 1;
    min-width: 0;
}

.profile-details h3 {
    margin: 0 0 4px 0;
    color: #1c1e21;
    font-size: 1.1rem;
    font-weight: 600;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.profile-details p {
    margin: 0 0 6px 0;
    color: #65676b;
    font-size: 0.9rem;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.profile-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    color: #42b883;
    font-weight: 500;
    background: rgba(66, 184, 131, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
}

.profile-status i {
    font-size: 0.7rem;
}

/* Grid de información estilo Facebook */
.profile-info-grid {
    padding: 8px 0;
    background: white;
}

.profile-info-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    transition: background-color 0.2s ease;
    cursor: default;
}

.profile-info-item:hover {
    background: #f8f9fa;
}

.profile-info-item .info-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #f0f2f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.profile-info-item .info-icon i {
    font-size: 0.9rem;
    color: #65676b;
}

.profile-info-item .info-content {
    flex: 1;
    min-width: 0;
}

.profile-info-item label {
    display: block;
    font-size: 0.9rem;
    color: #1c1e21;
    font-weight: 500;
    margin-bottom: 2px;
    line-height: 1.2;
}

.profile-info-item span {
    display: block;
    font-size: 0.85rem;
    color: #65676b;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Acciones del perfil estilo Facebook */
.profile-actions {
    padding: 8px 0;
    border-top: 1px solid #e4e6ea;
    background: white;
}

.profile-action-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-family: inherit;
}

.profile-action-item:hover {
    background: #f8f9fa;
}

.profile-action-item .action-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.profile-action-item.edit .action-icon {
    background: #e3f2fd;
}

.profile-action-item.edit .action-icon i {
    color: #1976d2;
    font-size: 0.9rem;
}

.profile-action-item.logout .action-icon {
    background: #ffebee;
}

.profile-action-item.logout .action-icon i {
    color: #d32f2f;
    font-size: 0.9rem;
}

.profile-action-item .action-content {
    flex: 1;
}

.profile-action-item .action-title {
    font-size: 0.95rem;
    color: #1c1e21;
    font-weight: 500;
    margin: 0;
    line-height: 1.2;
}

.profile-action-item .action-description {
    font-size: 0.8rem;
    color: #65676b;
    margin: 2px 0 0 0;
    line-height: 1.3;
}

/* Responsive */
@media (max-width: 768px) {
    .profile-dropdown {
        width: 320px;
        right: -20px;
    }

    .profile-btn-header {
        width: 36px;
        height: 36px;
    }

    .profile-btn-header .profile-avatar-btn {
        width: 28px;
        height: 28px;
        font-size: 0.9rem;
    }

    .profile-user-info {
        padding: 12px 16px;
    }

    .profile-avatar {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .profile-details h3 {
        font-size: 1rem;
    }

    .profile-details p {
        font-size: 0.85rem;
    }

    .profile-info-item {
        padding: 10px 16px;
    }

    .profile-action-item {
        padding: 10px 16px;
    }
}

@media (max-width: 480px) {
    .profile-dropdown {
        width: 280px;
        right: -40px;
    }

    .profile-btn-header {
        width: 32px;
        height: 32px;
    }

    .profile-btn-header .profile-avatar-btn {
        width: 24px;
        height: 24px;
        font-size: 0.8rem;
    }

    .profile-dropdown-header {
        padding: 12px 16px 8px;
    }

    .profile-dropdown-title {
        font-size: 1rem;
    }

    .profile-user-info {
        padding: 10px 16px;
    }

    .profile-avatar {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
    }

    .profile-details h3 {
        font-size: 0.95rem;
    }

    .profile-details p {
        font-size: 0.8rem;
    }

    .profile-info-item {
        padding: 8px 16px;
    }

    .profile-info-item .info-icon {
        width: 32px;
        height: 32px;
    }

    .profile-info-item label {
        font-size: 0.85rem;
    }

    .profile-info-item span {
        font-size: 0.8rem;
    }

    .profile-action-item {
        padding: 8px 16px;
    }

    .profile-action-item .action-icon {
        width: 32px;
        height: 32px;
    }

    .profile-action-item .action-title {
        font-size: 0.9rem;
    }

    .profile-action-item .action-description {
        font-size: 0.75rem;
    }
}

/* Animaciones */
@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutToRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.profile-tab-content.opening {
    animation: slideInFromRight 0.3s ease forwards;
}

.profile-tab-content.closing {
    animation: slideOutToRight 0.3s ease forwards;
}

/* Scrollbar personalizada */
.profile-tab-content::-webkit-scrollbar {
    width: 4px;
}

.profile-tab-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.profile-tab-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.profile-tab-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
