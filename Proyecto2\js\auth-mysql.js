// ========================================
// SISTEMA DE AUTENTICACIÓN AVISOFT - MYSQL
// ========================================

// Configuración
const AUTH_CONFIG_MYSQL = {
    SESSION_KEY: 'avisoft_session_token',
    USER_KEY: 'avisoft_user_data',
    REMEMBER_KEY: 'avisoft_remember_me',
    API_BASE_URL: 'api/', // Ruta base para las APIs
    SESSION_DURATION: 3600000, // 1 hora en milisegundos
    REMEMBER_DURATION: 2592000000 // 30 días en milisegundos
};

// Clase para manejar las peticiones a la API
class AuthAPI {
    constructor() {
        this.baseURL = AUTH_CONFIG_MYSQL.API_BASE_URL;
    }

    async makeRequest(endpoint, method = 'POST', data = null) {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
            };

            // Agregar token de autorización si existe
            const token = this.getStoredToken();
            if (token) {
                options.headers['Authorization'] = `Bearer ${token}`;
            }

            // Agregar datos si es necesario
            if (data && (method === 'POST' || method === 'PUT')) {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(this.baseURL + endpoint, options);
            
            // Verificar si la respuesta es JSON válida
            let result;
            try {
                result = await response.json();
            } catch (e) {
                result = { message: 'Error en la respuesta del servidor' };
            }

            return {
                success: response.ok,
                status: response.status,
                data: result
            };

        } catch (error) {
            console.error('Error en petición API:', error);
            return {
                success: false,
                status: 0,
                data: { message: 'Error de conexión con el servidor' }
            };
        }
    }

    getStoredToken() {
        return localStorage.getItem(AUTH_CONFIG_MYSQL.SESSION_KEY);
    }

    async register(userData) {
        return await this.makeRequest('register.php', 'POST', userData);
    }

    async login(email, password, remember = false) {
        return await this.makeRequest('login.php', 'POST', {
            email,
            password,
            remember
        });
    }

    async verifySession(token = null) {
        const sessionToken = token || this.getStoredToken();
        if (!sessionToken) {
            return { success: false, data: { message: 'No hay token de sesión' } };
        }

        return await this.makeRequest('verify-session.php', 'POST', {
            token: sessionToken
        });
    }

    async logout() {
        const token = this.getStoredToken();
        if (!token) {
            return { success: true, data: { message: 'No hay sesión activa' } };
        }

        return await this.makeRequest('logout.php', 'POST', {
            token: token
        });
    }
}

// Instancia global de la API
const authAPI = new AuthAPI();

// ========================================
// FUNCIONES DE AUTENTICACIÓN MYSQL
// ========================================

// Registrar usuario
async function registerUserMySQL(userData) {
    try {
        showLoadingState('Registrando usuario...');
        
        const response = await authAPI.register(userData);
        
        hideLoadingState();
        
        if (response.success) {
            showNotification('Usuario registrado exitosamente', 'success');
            return { success: true, data: response.data };
        } else {
            showNotification(response.data.message || 'Error al registrar usuario', 'error');
            return { success: false, message: response.data.message };
        }
        
    } catch (error) {
        hideLoadingState();
        console.error('Error en registro:', error);
        showNotification('Error de conexión', 'error');
        return { success: false, message: 'Error de conexión' };
    }
}

// Iniciar sesión
async function loginUserMySQL(email, password, remember = false) {
    try {
        showLoadingState('Iniciando sesión...');
        
        const response = await authAPI.login(email, password, remember);
        
        hideLoadingState();
        
        if (response.success) {
            // Guardar datos de sesión
            const { user, session_token } = response.data;
            
            localStorage.setItem(AUTH_CONFIG_MYSQL.SESSION_KEY, session_token);
            localStorage.setItem(AUTH_CONFIG_MYSQL.USER_KEY, JSON.stringify(user));
            
            if (remember) {
                localStorage.setItem(AUTH_CONFIG_MYSQL.REMEMBER_KEY, 'true');
            }
            
            showNotification('Inicio de sesión exitoso', 'success');
            
            // Actualizar interfaz
            updateAuthInterface(user);
            
            return { success: true, user: user };
            
        } else {
            showNotification(response.data.message || 'Error al iniciar sesión', 'error');
            return { success: false, message: response.data.message };
        }
        
    } catch (error) {
        hideLoadingState();
        console.error('Error en login:', error);
        showNotification('Error de conexión', 'error');
        return { success: false, message: 'Error de conexión' };
    }
}

// Verificar sesión actual
async function verifyCurrentSession() {
    try {
        const token = localStorage.getItem(AUTH_CONFIG_MYSQL.SESSION_KEY);
        if (!token) {
            return { success: false, message: 'No hay sesión activa' };
        }
        
        const response = await authAPI.verifySession(token);
        
        if (response.success) {
            const user = response.data.user;
            localStorage.setItem(AUTH_CONFIG_MYSQL.USER_KEY, JSON.stringify(user));
            updateAuthInterface(user);
            return { success: true, user: user };
        } else {
            // Sesión inválida, limpiar datos
            clearAuthData();
            return { success: false, message: response.data.message };
        }
        
    } catch (error) {
        console.error('Error al verificar sesión:', error);
        return { success: false, message: 'Error de conexión' };
    }
}

// Cerrar sesión
async function logoutUserMySQL() {
    try {
        showLoadingState('Cerrando sesión...');
        
        const response = await authAPI.logout();
        
        hideLoadingState();
        
        // Limpiar datos locales independientemente de la respuesta
        clearAuthData();
        
        // Actualizar interfaz
        updateAuthInterface(null);
        
        showNotification('Sesión cerrada exitosamente', 'success');
        
        return { success: true };
        
    } catch (error) {
        hideLoadingState();
        console.error('Error en logout:', error);
        
        // Limpiar datos locales aunque haya error
        clearAuthData();
        updateAuthInterface(null);
        
        showNotification('Sesión cerrada', 'warning');
        return { success: false, message: 'Error de conexión' };
    }
}

// Limpiar datos de autenticación
function clearAuthData() {
    localStorage.removeItem(AUTH_CONFIG_MYSQL.SESSION_KEY);
    localStorage.removeItem(AUTH_CONFIG_MYSQL.USER_KEY);
    localStorage.removeItem(AUTH_CONFIG_MYSQL.REMEMBER_KEY);
}

// Obtener usuario actual
function getCurrentUserMySQL() {
    const userData = localStorage.getItem(AUTH_CONFIG_MYSQL.USER_KEY);
    return userData ? JSON.parse(userData) : null;
}

// Verificar si hay sesión activa
function isLoggedInMySQL() {
    const token = localStorage.getItem(AUTH_CONFIG_MYSQL.SESSION_KEY);
    const user = getCurrentUserMySQL();
    return !!(token && user);
}

// ========================================
// FUNCIONES DE INTERFAZ
// ========================================

// Actualizar interfaz de autenticación
function updateAuthInterface(user) {
    const authButtons = document.getElementById('authButtons');
    const userMenu = document.getElementById('userMenu');
    
    if (user) {
        // Usuario logueado
        if (authButtons) authButtons.style.display = 'none';
        if (userMenu) {
            userMenu.style.display = 'flex';
            
            // Actualizar información del usuario
            const userName = document.getElementById('userName');
            const userFullName = document.getElementById('userFullName');
            const userEmail = document.getElementById('userEmail');
            
            if (userName) userName.textContent = user.nombre;
            if (userFullName) userFullName.textContent = `${user.nombre} ${user.apellido}`;
            if (userEmail) userEmail.textContent = user.email;
        }
    } else {
        // Usuario no logueado
        if (authButtons) authButtons.style.display = 'flex';
        if (userMenu) userMenu.style.display = 'none';
    }
}

// Mostrar estado de carga
function showLoadingState(message = 'Cargando...') {
    // Crear overlay de carga si no existe
    let loadingOverlay = document.getElementById('loadingOverlay');
    if (!loadingOverlay) {
        loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'loadingOverlay';
        loadingOverlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p id="loadingMessage">${message}</p>
            </div>
        `;
        loadingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;
        document.body.appendChild(loadingOverlay);
    } else {
        document.getElementById('loadingMessage').textContent = message;
        loadingOverlay.style.display = 'flex';
    }
}

// Ocultar estado de carga
function hideLoadingState() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

// ========================================
// INICIALIZACIÓN
// ========================================

// Verificar sesión al cargar la página
document.addEventListener('DOMContentLoaded', async function() {
    // Verificar si hay una sesión activa
    if (isLoggedInMySQL()) {
        await verifyCurrentSession();
    } else {
        updateAuthInterface(null);
    }
});

// Exportar funciones para uso global
window.AuthMySQL = {
    register: registerUserMySQL,
    login: loginUserMySQL,
    logout: logoutUserMySQL,
    verifySession: verifyCurrentSession,
    getCurrentUser: getCurrentUserMySQL,
    isLoggedIn: isLoggedInMySQL,
    updateInterface: updateAuthInterface
};
