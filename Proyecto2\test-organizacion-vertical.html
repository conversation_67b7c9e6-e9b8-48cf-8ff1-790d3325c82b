<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AviSoft - Prueba Organización Vertical</title>
    <!-- Archivos CSS reorganizados -->
    <link rel="stylesheet" href="css/layout-general.css">
    <link rel="stylesheet" href="css/notificaciones.css">
    <link rel="stylesheet" href="css/inicio-styles.css">
    <link rel="stylesheet" href="css/informacion-tarjetas.css">
    <link rel="stylesheet" href="css/elementos-comunes.css">
    <link rel="stylesheet" href="css/introduccion-styles.css">
    <link rel="stylesheet" href="css/informacion-styles.css">
    <link rel="stylesheet" href="css/secciones-principales-styles.css">
    <link rel="stylesheet" href="css/pasos-navegacion-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <aside>
        <div class="logo">
            <img src="assetslogo.png.png" alt="AviSoft Logo">
        </div>
        <nav class="menu">
            <a href="index.html"><i class="fas fa-home"></i> Inicio</a>
            <a href="introduccion.html"><i class="fas fa-info-circle"></i> Introducción</a>
            <a href="Informacion.html" class="active"><i class="fas fa-lightbulb"></i> Deberías saber que...</a>
            <a href="#"><i class="fas fa-cog"></i> Configuración</a>
        </nav>
    </aside>

    <main>
        <div class="top-bar">
            <div class="search-container">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="Buscar información, guías, protocolos...">
            </div>
            <div class="top-bar-actions">
                <div class="action-button notification-btn" id="notificationBtn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </div>

                <!-- Ventana emergente de notificaciones -->
                <div class="notification-dropdown" id="notificationDropdown">
                    <div class="notification-title-bar">
                        <h3>Notificaciones</h3>
                        <button class="close-btn" id="closeNotifications">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="notification-list">
                        <div class="notification-item">
                            <div class="notification-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="notification-content">
                                <h4 class="notification-title">Organización actualizada</h4>
                                <p class="notification-message">Las tarjetas ahora están organizadas verticalmente una debajo de la otra.</p>
                                <span class="notification-time">Ahora</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <section class="info-section">
            <h2 class="section-title">✅ Organización Vertical - Tarjetas una debajo de la otra</h2>
            <p class="section-description">Las tarjetas de información ahora están perfectamente organizadas de forma vertical, una debajo de la otra, para una mejor experiencia de lectura y navegación.</p>

            <div class="info-cards">
                <!-- Tarjeta 1: Control Ambiental -->
                <div class="info-card" data-section="control-ambiental">
                    <div class="info-card-header">
                        <div class="info-card-icon">
                            <i class="fas fa-temperature-high"></i>
                        </div>
                        <h3>1. Control Ambiental</h3>
                    </div>
                    <div class="info-card-preview">
                        <p>Primera tarjeta organizada verticalmente. Guía completa para el manejo ambiental en galpones de pollos de engorde.</p>
                        <button class="toggle-btn" onclick="toggleContent('control-ambiental')">Ver guía completa</button>
                    </div>

                    <!-- Contenido desplegable -->
                    <div id="control-ambiental" class="collapsible-content">
                        <div class="guide-content">
                            <div class="guide-intro">
                                <h3>Contenido de Control Ambiental</h3>
                                <p>Esta tarjeta se expande manteniendo la organización vertical del resto de las tarjetas.</p>
                            </div>

                            <!-- Botón para cerrar -->
                            <div class="close-guide-container">
                                <button class="close-guide-btn" onclick="toggleContent('control-ambiental')">Cerrar guía</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tarjeta 2: Nutrición Especializada -->
                <div class="info-card" data-section="nutricion-especializada">
                    <div class="info-card-header">
                        <div class="info-card-icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <h3>2. Nutrición Especializada</h3>
                    </div>
                    <div class="info-card-preview">
                        <p>Segunda tarjeta posicionada debajo de la primera. Información sobre alimentación balanceada y programas nutricionales.</p>
                        <button class="toggle-btn" onclick="toggleContent('nutricion-especializada')">Ver guía completa</button>
                    </div>

                    <!-- Contenido desplegable -->
                    <div id="nutricion-especializada" class="collapsible-content">
                        <div class="guide-content">
                            <div class="guide-intro">
                                <h3>Contenido de Nutrición Especializada</h3>
                                <p>Esta es la segunda tarjeta, perfectamente alineada debajo de la primera.</p>
                            </div>

                            <!-- Botón para cerrar -->
                            <div class="close-guide-container">
                                <button class="close-guide-btn" onclick="toggleContent('nutricion-especializada')">Cerrar guía</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tarjeta 3: Salud y Prevención -->
                <div class="info-card" data-section="salud-prevencion">
                    <div class="info-card-header">
                        <div class="info-card-icon">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <h3>3. Salud y Prevención</h3>
                    </div>
                    <div class="info-card-preview">
                        <p>Tercera tarjeta en la secuencia vertical. Protocolos de bioseguridad y prevención de enfermedades.</p>
                        <button class="toggle-btn" onclick="toggleContent('salud-prevencion')">Ver guía completa</button>
                    </div>

                    <!-- Contenido desplegable -->
                    <div id="salud-prevencion" class="collapsible-content">
                        <div class="guide-content">
                            <div class="guide-intro">
                                <h3>Contenido de Salud y Prevención</h3>
                                <p>Tercera tarjeta manteniendo la perfecta organización vertical.</p>
                            </div>

                            <!-- Botón para cerrar -->
                            <div class="close-guide-container">
                                <button class="close-guide-btn" onclick="toggleContent('salud-prevencion')">Cerrar guía</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tarjeta 4: Diseño de Galpones -->
                <div class="info-card" data-section="diseno-galpones">
                    <div class="info-card-header">
                        <div class="info-card-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h3>4. Diseño de Galpones</h3>
                    </div>
                    <div class="info-card-preview">
                        <p>Cuarta tarjeta en la organización vertical. Especificaciones técnicas para construcción y diseño óptimo.</p>
                        <button class="toggle-btn" onclick="toggleContent('diseno-galpones')">Ver guía completa</button>
                    </div>

                    <!-- Contenido desplegable -->
                    <div id="diseno-galpones" class="collapsible-content">
                        <div class="guide-content">
                            <div class="guide-intro">
                                <h3>Contenido de Diseño de Galpones</h3>
                                <p>Cuarta tarjeta perfectamente alineada en la secuencia vertical.</p>
                            </div>

                            <!-- Botón para cerrar -->
                            <div class="close-guide-container">
                                <button class="close-guide-btn" onclick="toggleContent('diseno-galpones')">Cerrar guía</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Mensaje de confirmación -->
        <section class="benefits-section">
            <div class="benefits-content">
                <h2 class="section-title">🎯 Organización Vertical Completada</h2>
                <ul class="benefits-list">
                    <li><span class="check-icon">✓</span> <strong>Organización vertical:</strong> Todas las tarjetas están una debajo de la otra</li>
                    <li><span class="check-icon">✓</span> <strong>Espaciado uniforme:</strong> Gap consistente entre todas las tarjetas</li>
                    <li><span class="check-icon">✓</span> <strong>Ancho completo:</strong> Las tarjetas ocupan todo el ancho disponible</li>
                    <li><span class="check-icon">✓</span> <strong>Responsive:</strong> Se adapta perfectamente a todos los tamaños de pantalla</li>
                    <li><span class="check-icon">✓</span> <strong>Funcionalidad preservada:</strong> Todas las características originales funcionan</li>
                </ul>
            </div>
        </section>

        <script>
            // Función para toggle del contenido
            function toggleContent(sectionId) {
                const content = document.getElementById(sectionId);
                const card = content.closest('.info-card');
                const container = document.querySelector('.info-cards');
                
                if (content.style.maxHeight && content.style.maxHeight !== '0px') {
                    // Cerrar
                    content.style.maxHeight = '0px';
                    card.classList.remove('expanded-card');
                    container.classList.remove('has-expanded-content');
                    content.classList.remove('expanded');
                } else {
                    // Cerrar otros contenidos abiertos
                    document.querySelectorAll('.collapsible-content').forEach(el => {
                        el.style.maxHeight = '0px';
                        el.classList.remove('expanded');
                    });
                    document.querySelectorAll('.info-card').forEach(el => {
                        el.classList.remove('expanded-card');
                    });
                    
                    // Abrir el seleccionado
                    content.style.maxHeight = content.scrollHeight + 'px';
                    card.classList.add('expanded-card');
                    container.classList.add('has-expanded-content');
                    content.classList.add('expanded');
                }
            }
        </script>
        <script src="notifications.js"></script>
    </main>
</body>
</html>
