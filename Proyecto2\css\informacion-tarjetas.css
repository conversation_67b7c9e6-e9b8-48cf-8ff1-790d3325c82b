/* ========================================
   ESTILOS PARA TARJETAS DE INFORMACIÓN
   ======================================== */

.info-cards {
  display: flex;
  flex-direction: column;
  margin-top: 20px;
  position: relative;
  align-items: flex-start;
  width: 100%; /* Asegura que use todo el ancho disponible */
}




/* Tarjeta de información */
.info-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  margin-bottom: 20px;
  width: 100%;
  max-width: 350px; /* Aumentamos ligeramente el ancho máximo */
  margin-left: 0;
  margin-right: auto; /* Fuerza la alineación hacia la izquierda */
}

.info-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  transform: translateY(-5px);
}

/* Header de la tarjeta */
.info-card-header {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.info-card-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e8f5e9;
  color: #2e7d32;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.info-card-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
  font-weight: 600;
}

/* Vista previa de la tarjeta */
.info-card-preview {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.info-card-preview p {
  color: #555;
  margin-bottom: 15px;
  line-height: 1.5;
  font-size: 14px;
}

/* Botón de toggle */
.toggle-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  font-size: 13px;
}

.toggle-btn:hover {
  background-color: #388e3c;
}

/* Contenido colapsable */
.collapsible-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease;
  width: 100%;
  position: relative;
  background-color: white;
  border-radius: 0 0 12px 12px;
}

.guide-content {
  padding: 25px;
}

/* Estados de tarjetas */
.info-card {
  transition: all 0.5s ease;
}

.expanded-card {
  z-index: 100;
  position: relative;
}

.has-expanded-content .info-card:not(.expanded-card) {
  opacity: 0.5;
  transform: scale(0.95);
}

/* Botón de cerrar guía */
.close-guide-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  margin-bottom: 10px;
}

.close-guide-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.close-guide-btn:hover {
  background-color: #c0392b;
}

.close-guide-btn:before {
  content: '\f00d';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  margin-right: 8px;
}

/* Introducción de guía */
.guide-intro {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 30px;
  border-left: 5px solid #4CAF50;
}

.guide-intro h3 {
  color: #2e7d32;
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.guide-intro p {
  color: #555;
  line-height: 1.6;
}

/* Secciones de fase */
.phase-section {
  margin-bottom: 30px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.phase-header {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.phase-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 15px;
}

.blue-marker {
  background-color: #1976d2;
}

.orange-marker {
  background-color: #ff9800;
}

.green-marker {
  background-color: #4caf50;
}

.phase-header h4 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.phase-content {
  padding: 20px;
}

/* Bloques de información */
.info-block {
  margin-bottom: 20px;
}

.info-block:last-child {
  margin-bottom: 0;
}

.info-block h5 {
  color: #2e7d32;
  margin-bottom: 12px;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
}

.info-block h5 i {
  margin-right: 8px;
  width: 20px;
  text-align: center;
}

.info-block ul {
  list-style-type: none;
  padding-left: 28px;
  margin: 0;
}

.info-block ul li {
  position: relative;
  padding-left: 15px;
  margin-bottom: 8px;
  line-height: 1.5;
  color: #555;
}

.info-block ul li:before {
  content: '•';
  color: #4CAF50;
  position: absolute;
  left: 0;
  font-weight: bold;
}

/* Responsive para pantallas grandes */
@media (min-width: 992px) {
  .expanded-card {
    width: 100%;
    max-width: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  }

  .collapsible-content.expanded {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
  }

  .expanded .guide-content {
    padding: 40px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .info-cards.has-expanded-content {
    padding-bottom: 50px;
  }

  .expanded .guide-intro {
    padding: 30px;
    margin-bottom: 40px;
    background-color: #e8f5e9;
    border-left-width: 8px;
  }

  .expanded .guide-intro h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
  }

  .expanded .guide-intro p {
    font-size: 1.1rem;
    line-height: 1.7;
  }

  .expanded .phase-section {
    margin-bottom: 50px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border-radius: 15px;
  }

  .expanded .phase-header {
    padding: 20px 25px;
    background-color: #f0f7f0;
  }

  .expanded .phase-header h4 {
    font-size: 1.4rem;
  }

  .expanded .phase-content {
    padding: 30px;
  }

  .expanded .info-block h5 {
    font-size: 1.2rem;
    margin-bottom: 15px;
  }

  .expanded .info-block ul li {
    margin-bottom: 12px;
    font-size: 1.05rem;
  }
}

/* Responsive para tablets y móviles */
@media (max-width: 768px) {
  .info-card {
    max-width: 100%;
  }

  .info-card-header {
    padding: 12px;
  }

  .info-card-preview {
    padding: 12px;
  }

  .guide-content {
    padding: 20px;
  }

  .phase-content {
    padding: 15px;
  }

  .guide-intro {
    padding: 15px;
    margin-bottom: 20px;
  }

  .phase-section {
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .info-card-header {
    padding: 10px;
  }

  .info-card-preview {
    padding: 10px;
  }

  .guide-content {
    padding: 15px;
  }

  .info-card-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .info-card-header h3 {
    font-size: 1rem;
  }

  .toggle-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}
