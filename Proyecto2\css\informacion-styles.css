/* ========================================
   ESTILOS PARA LA SECCIÓN DE INFORMACIÓN
   ======================================== */

/* Sección de información completa */
.info-section-full {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e9 100%);
  padding: 40px;
  margin-bottom: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.info-section-full::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #66bb6a, #4caf50, #388e3c);
}

/* Header de la sección de información */
.info-header {
  text-align: center;
  margin-bottom: 40px;
}

.info-icon {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #66bb6a, #4caf50);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30px;
  box-shadow: 0 10px 30px rgba(102, 187, 106, 0.3);
  transition: transform 0.3s ease;
}

.info-icon:hover {
  transform: scale(1.1) rotate(-5deg);
}

.info-icon i {
  font-size: 4rem;
  color: white;
}

.info-header h2 {
  color: #2e7d32;
  font-size: 2.5rem;
  margin-bottom: 20px;
  font-weight: 700;
}

/* Contenido de información */
.info-content-full {
  max-width: 1200px;
  margin: 0 auto;
}

.info-description {
  text-align: center;
  margin-bottom: 40px;
}

.info-description p {
  font-size: 1.2rem;
  color: #555;
  line-height: 1.7;
  margin-bottom: 15px;
}

/* Imagen de información */
.info-image-full {
  position: relative;
  margin: 40px 0;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  height: 400px;
}

.info-image-full img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.info-image-full:hover img {
  transform: scale(1.05);
}

/* Sección de categorías */
.categories-section {
  margin: 50px 0;
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.categories-section h3 {
  color: #2e7d32;
  font-size: 1.8rem;
  text-align: center;
  margin-bottom: 40px;
  font-weight: 600;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
}

.category-item {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 5px solid #4caf50;
}

.category-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  background: white;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.category-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.category-icon i {
  font-size: 2.5rem;
  color: #4caf50;
}

.category-header h4 {
  color: #2e7d32;
  font-size: 1.5rem;
  margin: 0;
  font-weight: 600;
  flex: 1;
}

.category-content p {
  color: #555;
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 20px;
}

/* Detalles de categoría */
.category-details {
  margin: 20px 0;
}

.category-details ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-details li {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 0;
}

.category-details li i {
  color: #4caf50;
  margin-right: 10px;
  font-size: 1.1rem;
}

.category-details li {
  color: #666;
  font-size: 1rem;
}

/* Etiquetas de categoría */
.category-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 20px;
}

.category-tags .tag {
  background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
  color: #2e7d32;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid #4caf50;
}

.category-tags .tag:hover {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

/* Resumen de sección */
.section-summary {
  margin: 50px 0;
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.section-summary h3 {
  color: #2e7d32;
  font-size: 1.8rem;
  margin-bottom: 30px;
  font-weight: 600;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.stat-item {
  background: linear-gradient(135deg, #f1f8e9, #e8f5e9);
  padding: 30px 20px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-top: 4px solid #4caf50;
}

.stat-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: #4caf50;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 1.1rem;
  color: #2e7d32;
  font-weight: 500;
}

/* Responsive para información */
@media (max-width: 768px) {
  .info-section-full {
    padding: 20px;
  }

  .info-header h2 {
    font-size: 2rem;
  }

  .info-icon {
    width: 100px;
    height: 100px;
  }

  .info-icon i {
    font-size: 3rem;
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .category-header {
    flex-direction: column;
    text-align: center;
  }

  .category-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .summary-stats {
    grid-template-columns: 1fr;
  }

  .stat-number {
    font-size: 2.5rem;
  }
}

/* ========================================
   ESTILOS PARA ORGANIZACIÓN VERTICAL
   ======================================== */

/* Sección de información principal - Organizada verticalmente */
.info-section {
  text-align: left;
  margin-bottom: 40px;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: stretch; /* Las tarjetas ocupan todo el ancho */
}

.section-title {
  color: #2e7d32;
  font-size: 2rem;
  margin-bottom: 15px;
  text-align: left;
  font-weight: 700;
  border-bottom: 3px solid #e8f5e9;
  padding-bottom: 10px;
  width: 100%;
}

.section-description {
  color: #555;
  font-size: 1.15rem;
  line-height: 1.7;
  margin-bottom: 35px;
  text-align: left;
  width: 100%;
  max-width: 800px; /* Limita el ancho para mejor legibilidad */
}

/* Mejoras para la organización vertical */
.info-section .info-cards {
  margin-top: 25px;
  width: 100%;
}

/* Responsive para organización vertical */
@media (max-width: 768px) {
  .section-title {
    font-size: 1.7rem;
  }

  .section-description {
    font-size: 1.05rem;
    max-width: 100%;
  }
}

/* ========================================
   ESTILOS PARA SECCIÓN DE INFORMACIÓN BÁSICA
   ======================================== */

/* Sección de información principal - Organizada y alineada a la izquierda */
.info-section {
  text-align: left;
  margin-bottom: 40px;
  padding: 0; /* Sin padding para maximizar el espacio */
}

.section-title {
  color: #2e7d32;
  font-size: 2rem;
  margin-bottom: 15px;
  text-align: left;
  font-weight: 700;
  border-bottom: 3px solid #e8f5e9;
  padding-bottom: 10px;
}

.section-description {
  color: #555;
  font-size: 1.15rem;
  line-height: 1.7;
  margin-bottom: 35px;
  text-align: left;
  max-width: 800px; /* Limita el ancho para mejor legibilidad */
}

/* Mejoras para la organización de la página */
.info-section .info-cards {
  margin-top: 25px;
}

/* Responsive para sección básica */
@media (max-width: 768px) {
  .section-title {
    font-size: 1.7rem;
  }

  .section-description {
    font-size: 1.05rem;
    max-width: 100%;
  }
}
