<?php
// ========================================
// API DE LOGIN DE USUARIOS AVISOFT
// ========================================

// Headers para CORS y JSON
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Solo permitir método POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

// Incluir dependencias
require_once __DIR__ . '/../classes/User.php';

try {
    // Obtener datos JSON del cuerpo de la petición
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    // Verificar que se recibieron datos
    if (!$data) {
        jsonResponse(['success' => false, 'message' => 'No se recibieron datos válidos'], 400);
    }

    // Validar campos requeridos
    if (empty($data['email'])) {
        jsonResponse(['success' => false, 'message' => 'El email es requerido'], 400);
    }

    if (empty($data['password'])) {
        jsonResponse(['success' => false, 'message' => 'La contraseña es requerida'], 400);
    }

    // Validar formato de email
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['success' => false, 'message' => 'El email no tiene un formato válido'], 400);
    }

    // Limpiar datos
    $email = strtolower(trim($data['email']));
    $password = $data['password'];
    $remember = isset($data['remember']) ? (bool)$data['remember'] : false;

    // Registrar intento de login
    logLoginAttempt($email, false, 'Intento iniciado');

    // Crear instancia de User
    $user = new User();

    // Intentar hacer login
    $result = $user->login($email, $password, $remember);

    if ($result['success']) {
        // Login exitoso
        logLoginAttempt($email, true, 'Login exitoso');
        
        // Preparar respuesta
        $response = [
            'success' => true,
            'message' => 'Inicio de sesión exitoso',
            'user' => $result['user'],
            'session_token' => $result['session_token'],
            'remember' => $remember
        ];

        // Establecer cookie de sesión si es necesario
        if ($remember) {
            setcookie(
                'avisoft_session', 
                $result['session_token'], 
                time() + REMEMBER_LIFETIME, 
                '/', 
                '', 
                isset($_SERVER['HTTPS']), 
                true
            );
        }

        jsonResponse($response, 200);

    } else {
        // Error en el login
        logLoginAttempt($email, false, $result['message']);
        
        // Determinar código de estado HTTP apropiado
        $status_code = 401; // Unauthorized por defecto
        
        if (strpos($result['message'], 'bloqueada') !== false) {
            $status_code = 423; // Locked
        } elseif (strpos($result['message'], 'desactivada') !== false) {
            $status_code = 403; // Forbidden
        }

        jsonResponse(['success' => false, 'message' => $result['message']], $status_code);
    }

} catch (PDOException $e) {
    // Error de base de datos
    error_log("Error de BD en login: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Error de base de datos'], 500);

} catch (Exception $e) {
    // Error general
    error_log("Error general en login: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Error interno del servidor'], 500);
}

/**
 * Función para registrar intentos de login
 */
function logLoginAttempt($email, $success, $message = null) {
    try {
        $log_data = [
            'email' => $email,
            'success' => $success,
            'message' => $message,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // En un entorno de producción, esto se guardaría en una tabla de logs
        error_log("Intento de login: " . json_encode($log_data));
        
    } catch (Exception $e) {
        error_log("Error al registrar intento de login: " . $e->getMessage());
    }
}

/**
 * Función para detectar actividad sospechosa
 */
function detectSuspiciousActivity($email, $ip) {
    // En un entorno de producción, aquí se implementaría lógica para:
    // - Detectar múltiples intentos desde la misma IP
    // - Detectar intentos desde ubicaciones geográficas inusuales
    // - Detectar patrones de ataque de fuerza bruta
    
    $suspicious_indicators = [];
    
    // Ejemplo: verificar si hay muchos intentos recientes desde esta IP
    // (esto requeriría una tabla de logs más robusta)
    
    return [
        'is_suspicious' => !empty($suspicious_indicators),
        'indicators' => $suspicious_indicators
    ];
}

/**
 * Función para validar la sesión existente
 */
function validateExistingSession($token) {
    try {
        $user = new User();
        $session = $user->verifySession($token);
        
        if ($session) {
            return [
                'valid' => true,
                'user' => [
                    'id' => $session['user_id'],
                    'nombre' => $session['nombre'],
                    'apellido' => $session['apellido'],
                    'email' => $session['email'],
                    'nombre_granja' => $session['nombre_granja']
                ]
            ];
        }
        
        return ['valid' => false];
        
    } catch (Exception $e) {
        error_log("Error al validar sesión: " . $e->getMessage());
        return ['valid' => false];
    }
}

/**
 * Función para limpiar sesiones expiradas
 */
function cleanExpiredSessions() {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "UPDATE sesiones_usuario SET activa = FALSE WHERE fecha_expiracion < NOW()";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        
        return $stmt->rowCount();
        
    } catch (Exception $e) {
        error_log("Error al limpiar sesiones: " . $e->getMessage());
        return 0;
    }
}

/**
 * Función para obtener estadísticas de login
 */
function getLoginStats($user_id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Obtener último acceso
        $query = "SELECT fecha_ultimo_acceso FROM usuarios WHERE id = :user_id";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $user_data = $stmt->fetch();
        
        // Contar sesiones activas
        $query = "SELECT COUNT(*) as sesiones_activas FROM sesiones_usuario 
                 WHERE usuario_id = :user_id AND activa = TRUE AND fecha_expiracion > NOW()";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $session_data = $stmt->fetch();
        
        return [
            'ultimo_acceso' => $user_data['fecha_ultimo_acceso'],
            'sesiones_activas' => $session_data['sesiones_activas']
        ];
        
    } catch (Exception $e) {
        error_log("Error al obtener estadísticas: " . $e->getMessage());
        return null;
    }
}

/**
 * Función para notificar login desde nueva ubicación
 */
function notifyNewLocationLogin($user_id, $email, $ip, $user_agent) {
    // En un entorno de producción, aquí se enviaría una notificación
    // por email o SMS si se detecta un login desde una nueva ubicación
    
    $notification_data = [
        'user_id' => $user_id,
        'email' => $email,
        'ip' => $ip,
        'user_agent' => $user_agent,
        'timestamp' => date('Y-m-d H:i:s'),
        'location' => getLocationFromIP($ip)
    ];
    
    error_log("Nuevo login detectado: " . json_encode($notification_data));
    
    return true;
}

/**
 * Función para obtener ubicación aproximada desde IP
 */
function getLocationFromIP($ip) {
    // En un entorno de producción, se usaría un servicio de geolocalización
    // como MaxMind GeoIP2 o similar
    
    return [
        'ip' => $ip,
        'country' => 'Unknown',
        'city' => 'Unknown',
        'provider' => 'Unknown'
    ];
}

/**
 * Función para generar código de verificación 2FA
 */
function generate2FACode() {
    return sprintf('%06d', mt_rand(0, 999999));
}

/**
 * Función para verificar si el usuario tiene 2FA habilitado
 */
function has2FAEnabled($user_id) {
    // En un entorno de producción, esto verificaría la configuración del usuario
    return false; // Por ahora deshabilitado
}
?>
