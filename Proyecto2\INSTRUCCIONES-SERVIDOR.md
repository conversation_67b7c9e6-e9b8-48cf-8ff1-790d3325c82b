# 🚀 Instrucciones para Iniciar el Servidor AviSoft

## ⚠️ **Problema Actual**
Estás usando **Python HTTP Server** que NO puede ejecutar archivos PHP. Por eso aparece el error "respuesta del servidor".

## ✅ **Solución: Usar Servidor PHP**

### **Opción 1: Script Automático (Recomendado)**

#### **Windows:**
1. **Hacer doble clic** en `start-server.bat`
2. **Si aparece error de PHP:** Instalar PHP primero (ver abajo)

#### **macOS/Linux:**
```bash
./start-server.sh
```

### **Opción 2: Manual**

#### **1. Verificar PHP:**
```bash
php --version
```

#### **2. Si tienes PHP:**
```bash
cd Proyecto2
php -S localhost:8000
```

#### **3. Si NO tienes PHP:**

**Windows:**
- **Opción A:** Descargar PHP desde https://windows.php.net/download/
- **Opción B:** Instalar XAMPP desde https://www.apachefriends.org/

**macOS:**
```bash
brew install php
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install php
```

## 🌐 **URLs una vez iniciado el servidor:**

- **🏠 Página principal:** http://localhost:8000/index.html
- **📝 Registro:** http://localhost:8000/registro.html
- **🔐 Login:** http://localhost:8000/login.html
- **🔍 Pruebas:** http://localhost:8000/test-api.html

## 🔧 **Verificar que Funciona:**

1. **Abrir:** http://localhost:8000/test-api.html
2. **Hacer clic:** "1. Probar PHP"
3. **Debería mostrar:** ✅ PHP funcionando

## 📋 **Diferencias:**

```
❌ ANTES (Python Server):
python -m http.server 8000
→ Solo archivos estáticos
→ PHP no funciona
→ APIs fallan

✅ DESPUÉS (PHP Server):
php -S localhost:8000
→ Ejecuta PHP
→ APIs funcionan
→ Base de datos conecta
```

## 🆘 **Si Sigues Teniendo Problemas:**

### **Error: "php no se reconoce"**
- **Instalar PHP** o **XAMPP**
- **Agregar PHP al PATH** del sistema

### **Error: "No se puede conectar"**
- **Verificar puerto 8000** esté libre
- **Probar puerto diferente:** `php -S localhost:8080`

### **Error: "Base de datos"**
- **Verificar MySQL** esté ejecutándose
- **Revisar configuración** en `config/database.php`

## 🎯 **Resumen Rápido:**

1. **Detener** servidor Python (Ctrl+C)
2. **Ejecutar** `start-server.bat` (Windows) o `./start-server.sh` (Mac/Linux)
3. **Abrir** http://localhost:8000/registro.html
4. **Probar registro** - debería funcionar sin errores

¡Una vez que uses el servidor PHP, el registro funcionará perfectamente! 🎉
