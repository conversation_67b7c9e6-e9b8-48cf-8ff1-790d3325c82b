<?php
// ========================================
// CONFIGURACIÓN DE BASE DE DATOS AVISOFT
// ========================================

// Configuración de la base de datos
define('DB_HOST', 'localhost');
define('DB_NAME', 'Avisoftdatabase');
define('DB_USER', 'root'); // Cambia si tienes otro usuario
define('DB_PASS', ''); // Cambia por tu contraseña de MySQL

// Configuración de la aplicación
define('APP_NAME', 'AviSoft');
define('APP_VERSION', '1.0.0');

// Configuración de seguridad
define('JWT_SECRET', 'avisoft_secret_key_2024'); // Cambia por una clave más segura
define('PASSWORD_SALT', 'avisoft_salt_2024'); // Cambia por un salt más seguro

// Configuración de sesiones
define('SESSION_LIFETIME', 3600); // 1 hora en segundos
define('REMEMBER_LIFETIME', 2592000); // 30 días en segundos

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $conn = null;

    /**
     * Obtener conexión a la base de datos
     */
    public function getConnection() {
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8",
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                )
            );
        } catch(PDOException $exception) {
            error_log("Error de conexión: " . $exception->getMessage());
            throw new Exception("Error de conexión a la base de datos");
        }
        return $this->conn;
    }

    /**
     * Cerrar conexión
     */
    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * Verificar si la conexión está activa
     */
    public function isConnected() {
        return $this->conn !== null;
    }

    /**
     * Obtener información de la base de datos
     */
    public function getDatabaseInfo() {
        try {
            $conn = $this->getConnection();
            $stmt = $conn->query("SELECT DATABASE() as db_name, VERSION() as version");
            return $stmt->fetch();
        } catch(Exception $e) {
            return false;
        }
    }
}

// Función para obtener una nueva instancia de la base de datos
function getDatabase() {
    return new Database();
}

// Función para hashear contraseñas de forma segura
function hashPassword($password) {
    return password_hash($password . PASSWORD_SALT, PASSWORD_HASH_DEFAULT);
}

// Función para verificar contraseñas
function verifyPassword($password, $hash) {
    return password_verify($password . PASSWORD_SALT, $hash);
}

// Función para generar tokens seguros
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// Función para validar email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Función para limpiar datos de entrada
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Función para responder con JSON
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// Función para manejar errores de base de datos
function handleDatabaseError($e, $operation = 'operación') {
    error_log("Error en $operation: " . $e->getMessage());
    
    // En producción, no mostrar detalles del error
    if (defined('ENVIRONMENT') && ENVIRONMENT === 'production') {
        return "Error interno del servidor";
    } else {
        return "Error en $operation: " . $e->getMessage();
    }
}

// Configurar zona horaria
date_default_timezone_set('America/Bogota');

// Configurar manejo de errores
error_reporting(E_ALL);
ini_set('display_errors', 0); // En producción debe ser 0
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');

// Crear directorio de logs si no existe
if (!file_exists(__DIR__ . '/../logs')) {
    mkdir(__DIR__ . '/../logs', 0755, true);
}
?>
