<?php
// ========================================
// CLASE USER - GESTIÓN DE USUARIOS AVISOFT
// ========================================

require_once __DIR__ . '/../config/database.php';

class User {
    private $conn;
    private $table_name = "usuarios";

    // Propiedades del usuario
    public $id;
    public $nombre;
    public $apellido;
    public $email;
    public $telefono;
    public $password_hash;
    public $fecha_registro;
    public $fecha_ultimo_acceso;
    public $activo;
    public $acepto_terminos;
    public $suscrito_newsletter;
    public $email_verificado;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Registrar nuevo usuario
     */
    public function register($data) {
        try {
            // Validar datos
            $validation = $this->validateRegistrationData($data);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }

            // Verificar si el email ya existe
            if ($this->emailExists($data['email'])) {
                return ['success' => false, 'message' => 'Ya existe una cuenta con este correo electrónico'];
            }

            // Preparar consulta
            $query = "INSERT INTO " . $this->table_name . "
                     (nombre, apellido, email, telefono, password_hash,
                      acepto_terminos, suscrito_newsletter, token_verificacion)
                     VALUES (:nombre, :apellido, :email, :telefono,
                             :password_hash, :acepto_terminos, :suscrito_newsletter, :token_verificacion)";

            $stmt = $this->conn->prepare($query);

            // Limpiar y hashear datos
            $nombre = sanitizeInput($data['nombre']);
            $apellido = sanitizeInput($data['apellido']);
            $email = strtolower(sanitizeInput($data['email']));
            $telefono = sanitizeInput($data['telefono'] ?? '');
            $password_hash = hashPassword($data['password']);
            $acepto_terminos = isset($data['acepto_terminos']) ? 1 : 0;
            $suscrito_newsletter = isset($data['suscrito_newsletter']) ? 1 : 0;
            $token_verificacion = generateSecureToken();

            // Bind de parámetros
            $stmt->bindParam(':nombre', $nombre);
            $stmt->bindParam(':apellido', $apellido);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':telefono', $telefono);
            $stmt->bindParam(':password_hash', $password_hash);
            $stmt->bindParam(':acepto_terminos', $acepto_terminos);
            $stmt->bindParam(':suscrito_newsletter', $suscrito_newsletter);
            $stmt->bindParam(':token_verificacion', $token_verificacion);

            // Ejecutar
            if ($stmt->execute()) {
                $user_id = $this->conn->lastInsertId();
                
                // Registrar actividad
                $this->logActivity($user_id, 'registro', 'Usuario registrado exitosamente');
                
                return [
                    'success' => true, 
                    'message' => 'Usuario registrado exitosamente',
                    'user_id' => $user_id,
                    'token_verificacion' => $token_verificacion
                ];
            } else {
                return ['success' => false, 'message' => 'Error al registrar usuario'];
            }

        } catch (Exception $e) {
            error_log("Error en registro: " . $e->getMessage());
            return ['success' => false, 'message' => 'Error interno del servidor'];
        }
    }

    /**
     * Iniciar sesión
     */
    public function login($email, $password, $remember = false) {
        try {
            // Validar datos
            if (empty($email) || empty($password)) {
                return ['success' => false, 'message' => 'Email y contraseña son requeridos'];
            }

            if (!validateEmail($email)) {
                return ['success' => false, 'message' => 'Email inválido'];
            }

            // Buscar usuario
            $user = $this->getUserByEmail($email);
            if (!$user) {
                return ['success' => false, 'message' => 'Credenciales incorrectas'];
            }

            // Verificar si está bloqueado
            if ($this->isUserBlocked($user['id'])) {
                return ['success' => false, 'message' => 'Cuenta temporalmente bloqueada por múltiples intentos fallidos'];
            }

            // Verificar contraseña
            if (!verifyPassword($password, $user['password_hash'])) {
                $this->incrementLoginAttempts($user['id']);
                return ['success' => false, 'message' => 'Credenciales incorrectas'];
            }

            // Verificar si está activo
            if (!$user['activo']) {
                return ['success' => false, 'message' => 'Cuenta desactivada'];
            }

            // Reset intentos de login
            $this->resetLoginAttempts($user['id']);

            // Crear sesión
            $session_token = $this->createSession($user['id'], $remember);
            
            // Actualizar último acceso
            $this->updateLastAccess($user['id']);

            // Registrar actividad
            $this->logActivity($user['id'], 'login', 'Inicio de sesión exitoso');

            return [
                'success' => true,
                'message' => 'Inicio de sesión exitoso',
                'user' => [
                    'id' => $user['id'],
                    'nombre' => $user['nombre'],
                    'apellido' => $user['apellido'],
                    'email' => $user['email']
                ],
                'session_token' => $session_token
            ];

        } catch (Exception $e) {
            error_log("Error en login: " . $e->getMessage());
            return ['success' => false, 'message' => 'Error interno del servidor'];
        }
    }

    /**
     * Verificar si el email existe
     */
    private function emailExists($email) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        return $stmt->rowCount() > 0;
    }

    /**
     * Obtener usuario por email
     */
    private function getUserByEmail($email) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE email = :email LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Validar datos de registro
     */
    private function validateRegistrationData($data) {
        if (empty($data['nombre'])) {
            return ['valid' => false, 'message' => 'El nombre es requerido'];
        }

        if (empty($data['apellido'])) {
            return ['valid' => false, 'message' => 'El apellido es requerido'];
        }

        if (empty($data['email']) || !validateEmail($data['email'])) {
            return ['valid' => false, 'message' => 'Email válido es requerido'];
        }

        if (empty($data['password']) || strlen($data['password']) < 8) {
            return ['valid' => false, 'message' => 'La contraseña debe tener al menos 8 caracteres'];
        }

        if ($data['password'] !== $data['confirm_password']) {
            return ['valid' => false, 'message' => 'Las contraseñas no coinciden'];
        }

        if (!isset($data['acepto_terminos'])) {
            return ['valid' => false, 'message' => 'Debe aceptar los términos y condiciones'];
        }

        return ['valid' => true];
    }

    /**
     * Crear sesión de usuario
     */
    private function createSession($user_id, $remember = false) {
        $token = generateSecureToken();
        $expiration = $remember ? 
            date('Y-m-d H:i:s', time() + REMEMBER_LIFETIME) : 
            date('Y-m-d H:i:s', time() + SESSION_LIFETIME);

        $query = "INSERT INTO sesiones_usuario 
                 (usuario_id, token_sesion, ip_address, user_agent, recordar_sesion, fecha_expiracion) 
                 VALUES (:usuario_id, :token, :ip, :user_agent, :remember, :expiration)";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':usuario_id', $user_id);
        $stmt->bindParam(':token', $token);
        $stmt->bindParam(':ip', $_SERVER['REMOTE_ADDR'] ?? 'unknown');
        $stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT'] ?? 'unknown');
        $stmt->bindParam(':remember', $remember);
        $stmt->bindParam(':expiration', $expiration);

        if ($stmt->execute()) {
            return $token;
        }
        return false;
    }

    /**
     * Verificar si el usuario está bloqueado
     */
    private function isUserBlocked($user_id) {
        $query = "SELECT bloqueado_hasta FROM " . $this->table_name . " 
                 WHERE id = :user_id AND bloqueado_hasta > NOW()";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        return $stmt->rowCount() > 0;
    }

    /**
     * Incrementar intentos de login
     */
    private function incrementLoginAttempts($user_id) {
        $query = "UPDATE " . $this->table_name . " 
                 SET intentos_login = intentos_login + 1,
                     bloqueado_hasta = CASE 
                         WHEN intentos_login >= 4 THEN DATE_ADD(NOW(), INTERVAL 15 MINUTE)
                         ELSE bloqueado_hasta 
                     END
                 WHERE id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
    }

    /**
     * Reset intentos de login
     */
    private function resetLoginAttempts($user_id) {
        $query = "UPDATE " . $this->table_name . " 
                 SET intentos_login = 0, bloqueado_hasta = NULL 
                 WHERE id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
    }

    /**
     * Actualizar último acceso
     */
    private function updateLastAccess($user_id) {
        $query = "UPDATE " . $this->table_name . " 
                 SET fecha_ultimo_acceso = NOW() 
                 WHERE id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
    }

    /**
     * Registrar actividad
     */
    private function logActivity($user_id, $action, $description = null) {
        $query = "INSERT INTO logs_actividad 
                 (usuario_id, accion, descripcion, ip_address, user_agent) 
                 VALUES (:user_id, :action, :description, :ip, :user_agent)";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':action', $action);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':ip', $_SERVER['REMOTE_ADDR'] ?? 'unknown');
        $stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT'] ?? 'unknown');
        $stmt->execute();
    }

    /**
     * Verificar sesión por token
     */
    public function verifySession($token) {
        $query = "SELECT s.*, u.id as user_id, u.nombre, u.apellido, u.email
                 FROM sesiones_usuario s
                 JOIN usuarios u ON s.usuario_id = u.id
                 WHERE s.token_sesion = :token
                 AND s.activa = TRUE
                 AND s.fecha_expiracion > NOW()
                 AND u.activo = TRUE";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':token', $token);
        $stmt->execute();
        
        return $stmt->fetch();
    }

    /**
     * Cerrar sesión
     */
    public function logout($token) {
        $query = "UPDATE sesiones_usuario SET activa = FALSE WHERE token_sesion = :token";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':token', $token);
        return $stmt->execute();
    }

    /**
     * Obtener información del usuario por ID
     */
    public function getUserById($user_id) {
        $query = "SELECT id, nombre, apellido, email, telefono,
                         fecha_registro, fecha_ultimo_acceso, suscrito_newsletter
                 FROM " . $this->table_name . "
                 WHERE id = :user_id AND activo = TRUE";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        return $stmt->fetch();
    }

    /**
     * Actualizar información del usuario
     */
    public function updateUser($user_id, $data) {
        try {
            $fields = [];
            $params = [':user_id' => $user_id];

            if (isset($data['nombre'])) {
                $fields[] = "nombre = :nombre";
                $params[':nombre'] = sanitizeInput($data['nombre']);
            }

            if (isset($data['apellido'])) {
                $fields[] = "apellido = :apellido";
                $params[':apellido'] = sanitizeInput($data['apellido']);
            }

            if (isset($data['telefono'])) {
                $fields[] = "telefono = :telefono";
                $params[':telefono'] = sanitizeInput($data['telefono']);
            }



            if (empty($fields)) {
                return ['success' => false, 'message' => 'No hay datos para actualizar'];
            }

            $query = "UPDATE " . $this->table_name . " SET " . implode(', ', $fields) . " WHERE id = :user_id";
            $stmt = $this->conn->prepare($query);

            if ($stmt->execute($params)) {
                $this->logActivity($user_id, 'actualizar_perfil', 'Información de perfil actualizada');
                return ['success' => true, 'message' => 'Información actualizada exitosamente'];
            } else {
                return ['success' => false, 'message' => 'Error al actualizar información'];
            }

        } catch (Exception $e) {
            error_log("Error al actualizar usuario: " . $e->getMessage());
            return ['success' => false, 'message' => 'Error interno del servidor'];
        }
    }
}
?>
