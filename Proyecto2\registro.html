<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="css/auth-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <title>Registro - AviSoft</title>
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="auth-card">
            <!-- Logo y título -->
            <div class="auth-header">
                <div class="auth-logo">
                    <img src="assetslogo.png.png" alt="AviSoft Logo">
                </div>
                <h1><PERSON><PERSON><PERSON></h1>
                <p>Únete a la comunidad de AviSoft</p>
            </div>

            <!-- Formulario de registro -->
            <form class="auth-form" id="registerForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">
                            <i class="fas fa-user"></i>
                            Nombre
                        </label>
                        <input type="text" id="firstName" name="firstName" required placeholder="Tu nombre">
                        <span class="error-message" id="firstNameError"></span>
                    </div>
                    <div class="form-group">
                        <label for="lastName">
                            <i class="fas fa-user"></i>
                            Apellido
                        </label>
                        <input type="text" id="lastName" name="lastName" required placeholder="Tu apellido">
                        <span class="error-message" id="lastNameError"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i>
                        Correo Electrónico
                    </label>
                    <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                    <span class="error-message" id="emailError"></span>
                </div>

                <div class="form-group">
                    <label for="phone">
                        <i class="fas fa-phone"></i>
                        Teléfono (Opcional)
                    </label>
                    <input type="tel" id="phone" name="phone" placeholder="+57 ************">
                    <span class="error-message" id="phoneError"></span>
                </div>

                
                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        Contraseña
                    </label>
                    <div class="password-input">
                        <input type="password" id="password" name="password" required placeholder="Mínimo 8 caracteres">
                        <button type="button" class="toggle-password" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="password-strength" id="passwordStrength">
                        <div class="strength-bar">
                            <div class="strength-fill"></div>
                        </div>
                        <span class="strength-text">Fortaleza de la contraseña</span>
                    </div>
                    <span class="error-message" id="passwordError"></span>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">
                        <i class="fas fa-lock"></i>
                        Confirmar Contraseña
                    </label>
                    <div class="password-input">
                        <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="Repite tu contraseña">
                        <button type="button" class="toggle-password" id="toggleConfirmPassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <span class="error-message" id="confirmPasswordError"></span>
                </div>

                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="acceptTerms" name="acceptTerms" required>
                        <span class="checkmark"></span>
                        Acepto los <a href="#" target="_blank">Términos y Condiciones</a>
                    </label>
                    <label class="checkbox-container">
                        <input type="checkbox" id="acceptNewsletter" name="acceptNewsletter">
                        <span class="checkmark"></span>
                        Quiero recibir información sobre actualizaciones y novedades
                    </label>
                </div>

                <button type="submit" class="auth-btn primary" id="registerBtn">
                    <i class="fas fa-user-plus"></i>
                    Crear Cuenta
                </button>

                <div class="auth-divider">
                    <span>o</span>
                </div>

                <div class="social-login">
                    <button type="button" class="social-btn google">
                        <i class="fab fa-google"></i>
                        Registrarse con Google
                    </button>
                    <button type="button" class="social-btn microsoft">
                        <i class="fab fa-microsoft"></i>
                        Registrarse con Microsoft
                    </button>
                </div>
            </form>

            <!-- Enlaces adicionales -->
            <div class="auth-footer">
                <p>¿Ya tienes una cuenta? <a href="login.html">Inicia sesión aquí</a></p>
                <p><a href="index.html">← Volver al inicio</a></p>
            </div>
        </div>

        <!-- Panel lateral informativo -->
        <div class="auth-info-panel">
            <div class="info-content">
                <h2>Únete a AviSoft</h2>
                <p>Regístrate para acceder a información especializada y herramientas avanzadas de gestión avícola.</p>

                <div class="info-features">
                    <div class="feature-item">
                        <i class="fas fa-warehouse"></i>
                        <div>
                            <h4>Diseño de Galpones</h4>
                            <p>Guías completas para estructuras, ventilación y equipamiento</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-shield-virus"></i>
                        <div>
                            <h4>Protocolos Sanitarios</h4>
                            <p>Bioseguridad, vacunación y prevención de enfermedades</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-drumstick-bite"></i>
                        <div>
                            <h4>Nutrición Especializada</h4>
                            <p>Programas alimentarios y requerimientos nutricionales</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-temperature-high"></i>
                        <div>
                            <h4>Control Ambiental</h4>
                            <p>Parámetros óptimos de temperatura, humedad y ventilación</p>
                        </div>
                    </div>
                </div>

                <div class="registration-benefits">
                    <h4>Beneficios del registro:</h4>
                    <ul>
                        <li><i class="fas fa-check"></i> Acceso completo a todas las guías</li>
                        <li><i class="fas fa-check"></i> Búsqueda avanzada personalizada</li>
                        <li><i class="fas fa-check"></i> Actualizaciones y notificaciones</li>
                        <li><i class="fas fa-check"></i> Soporte técnico especializado</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Notificación de estado -->
    <div class="notification" id="notification">
        <div class="notification-content">
            <i class="fas fa-check-circle"></i>
            <span id="notificationMessage"></span>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/auth-mysql.js"></script>

    <!-- Script específico para registro con MySQL -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const registerForm = document.getElementById('registerForm');

            if (registerForm) {
                registerForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const formData = new FormData(registerForm);
                    const userData = {
                        nombre: formData.get('firstName'),
                        apellido: formData.get('lastName'),
                        email: formData.get('email'),
                        telefono: formData.get('phone'),
                        password: formData.get('password'),
                        confirm_password: formData.get('confirmPassword'),
                        acepto_terminos: formData.get('acceptTerms') === 'on',
                        suscrito_newsletter: formData.get('acceptNewsletter') === 'on'
                    };

                    // Validaciones básicas
                    if (!userData.nombre || !userData.apellido || !userData.email || !userData.password) {
                        showNotification('Por favor complete todos los campos obligatorios', 'error');
                        return;
                    }

                    if (!validateEmail(userData.email)) {
                        showNotification('Por favor ingrese un email válido', 'error');
                        return;
                    }

                    if (userData.password !== userData.confirm_password) {
                        showNotification('Las contraseñas no coinciden', 'error');
                        return;
                    }

                    if (!userData.acepto_terminos) {
                        showNotification('Debe aceptar los términos y condiciones', 'error');
                        return;
                    }

                    // Intentar registro con MySQL
                    const result = await AuthMySQL.register(userData);

                    if (result.success) {
                        // Redirigir a login después de un breve delay
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 2000);
                    }
                });
            }
        });
    </script>
</body>
</html>
