<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="css/layout-general.css">
    <link rel="stylesheet" href="css/auth-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <title>Registro - AviSoft</title>
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="auth-card">
            <!-- Logo y título -->
            <div class="auth-header">
                <div class="auth-logo">
                    <img src="assetslogo.png.png" alt="AviSoft Logo">
                </div>
                <h1><PERSON>rea<PERSON></h1>
                <p>Únete a la comunidad de AviSoft</p>
            </div>

            <!-- Formulario de registro -->
            <form class="auth-form" id="registerForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">
                            <i class="fas fa-user"></i>
                            Nombre
                        </label>
                        <input type="text" id="firstName" name="firstName" required placeholder="Tu nombre">
                        <span class="error-message" id="firstNameError"></span>
                    </div>
                    <div class="form-group">
                        <label for="lastName">
                            <i class="fas fa-user"></i>
                            Apellido
                        </label>
                        <input type="text" id="lastName" name="lastName" required placeholder="Tu apellido">
                        <span class="error-message" id="lastNameError"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i>
                        Correo Electrónico
                    </label>
                    <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                    <span class="error-message" id="emailError"></span>
                </div>

                <div class="form-group">
                    <label for="phone">
                        <i class="fas fa-phone"></i>
                        Teléfono (Opcional)
                    </label>
                    <input type="tel" id="phone" name="phone" placeholder="+57 ************">
                    <span class="error-message" id="phoneError"></span>
                </div>

                <div class="form-group">
                    <label for="farmName">
                        <i class="fas fa-warehouse"></i>
                        Nombre de la Granja (Opcional)
                    </label>
                    <input type="text" id="farmName" name="farmName" placeholder="Granja Los Pollos">
                    <span class="error-message" id="farmNameError"></span>
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        Contraseña
                    </label>
                    <div class="password-input">
                        <input type="password" id="password" name="password" required placeholder="Mínimo 8 caracteres">
                        <button type="button" class="toggle-password" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="password-strength" id="passwordStrength">
                        <div class="strength-bar">
                            <div class="strength-fill"></div>
                        </div>
                        <span class="strength-text">Fortaleza de la contraseña</span>
                    </div>
                    <span class="error-message" id="passwordError"></span>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">
                        <i class="fas fa-lock"></i>
                        Confirmar Contraseña
                    </label>
                    <div class="password-input">
                        <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="Repite tu contraseña">
                        <button type="button" class="toggle-password" id="toggleConfirmPassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <span class="error-message" id="confirmPasswordError"></span>
                </div>

                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="acceptTerms" name="acceptTerms" required>
                        <span class="checkmark"></span>
                        Acepto los <a href="#" target="_blank">Términos y Condiciones</a>
                    </label>
                    <label class="checkbox-container">
                        <input type="checkbox" id="acceptNewsletter" name="acceptNewsletter">
                        <span class="checkmark"></span>
                        Quiero recibir información sobre actualizaciones y novedades
                    </label>
                </div>

                <button type="submit" class="auth-btn primary" id="registerBtn">
                    <i class="fas fa-user-plus"></i>
                    Crear Cuenta
                </button>

                <div class="auth-divider">
                    <span>o</span>
                </div>

                <div class="social-login">
                    <button type="button" class="social-btn google">
                        <i class="fab fa-google"></i>
                        Registrarse con Google
                    </button>
                    <button type="button" class="social-btn microsoft">
                        <i class="fab fa-microsoft"></i>
                        Registrarse con Microsoft
                    </button>
                </div>
            </form>

            <!-- Enlaces adicionales -->
            <div class="auth-footer">
                <p>¿Ya tienes una cuenta? <a href="login.html">Inicia sesión aquí</a></p>
                <p><a href="index.html">← Volver al inicio</a></p>
            </div>
        </div>

        <!-- Panel lateral informativo -->
        <div class="auth-info-panel">
            <div class="info-content">
                <h2>¿Por qué unirse a AviSoft?</h2>
                <p>Accede a la plataforma más completa de información avícola especializada.</p>
                
                <div class="info-features">
                    <div class="feature-item">
                        <i class="fas fa-graduation-cap"></i>
                        <div>
                            <h4>Conocimiento Especializado</h4>
                            <p>Información desarrollada por veterinarios y expertos en avicultura</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <div>
                            <h4>Mejora tu Productividad</h4>
                            <p>Optimiza tus resultados con protocolos probados científicamente</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-mobile-alt"></i>
                        <div>
                            <h4>Acceso 24/7</h4>
                            <p>Consulta información desde cualquier dispositivo, en cualquier momento</p>
                        </div>
                    </div>
                </div>

                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number">5,000+</div>
                        <div class="stat-label">Avicultores registrados</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">Guías especializadas</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">98%</div>
                        <div class="stat-label">Satisfacción de usuarios</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notificación de estado -->
    <div class="notification" id="notification">
        <div class="notification-content">
            <i class="fas fa-check-circle"></i>
            <span id="notificationMessage"></span>
        </div>
    </div>

    <script src="js/auth.js"></script>
</body>
</html>
