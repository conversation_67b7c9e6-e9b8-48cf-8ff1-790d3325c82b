/* ========================================
   ESTILOS PARA NOTIFICACIONES
   ======================================== */

/* Ventana emergente de notificaciones */
.notification-dropdown {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 320px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.15);
  z-index: 100;
  overflow: hidden;
  display: none;
  animation: fadeIn 0.3s ease;
  padding-top: 5px;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(-10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

/* Barra de título de notificaciones */
.notification-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 15px 5px;
}

.notification-title-bar h3 {
  margin: 0;
  font-size: 16px;
  color: #2e7d32;
  font-weight: 600;
}

.notification-title-bar .close-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  transition: color 0.3s ease;
}

.notification-title-bar .close-btn:hover {
  color: #2e7d32;
}

/* Lista de notificaciones */
.notification-list {
  max-height: 350px;
  overflow-y: auto;
  padding-top: 5px;
}

.notification-item {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: flex-start;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f9f9f9;
}

.notification-item:last-child {
  border-bottom: none;
}

/* Icono de notificación */
.notification-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #e8f5e9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.notification-icon i {
  color: #2e7d32;
  font-size: 16px;
}

/* Contenido de notificación */
.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #333;
}

.notification-message {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.notification-time {
  font-size: 11px;
  color: #999;
  margin-top: 5px;
  display: block;
}

/* Footer de notificaciones */
.notification-footer {
  padding: 10px 15px;
  text-align: center;
  border-top: 1px solid #eee;
  background-color: #f9f9f9;
}

.notification-footer a {
  color: #2e7d32;
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
  transition: text-decoration 0.3s ease;
}

.notification-footer a:hover {
  text-decoration: underline;
}

/* Badge de notificaciones no leídas */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  min-width: 18px;
  height: 18px;
  padding: 0 4px;
  background-color: #f44336;
  color: white;
  border-radius: 50%;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  pointer-events: none;
  border: 1px solid white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(244, 67, 54, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
  }
}

/* Estados de notificación */
.notification-item.unread {
  background-color: #f8f9fa;
  border-left: 3px solid #4caf50;
}

.notification-item.unread .notification-title {
  color: #2e7d32;
  font-weight: 700;
}

/* Tipos de notificación */
.notification-item.info .notification-icon {
  background-color: #e3f2fd;
}

.notification-item.info .notification-icon i {
  color: #1976d2;
}

.notification-item.warning .notification-icon {
  background-color: #fff3e0;
}

.notification-item.warning .notification-icon i {
  color: #f57c00;
}

.notification-item.error .notification-icon {
  background-color: #ffebee;
}

.notification-item.error .notification-icon i {
  color: #d32f2f;
}

.notification-item.success .notification-icon {
  background-color: #e8f5e9;
}

.notification-item.success .notification-icon i {
  color: #388e3c;
}

/* Scrollbar personalizada para la lista de notificaciones */
.notification-list::-webkit-scrollbar {
  width: 6px;
}

.notification-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive para notificaciones */
@media (max-width: 768px) {
  .notification-dropdown {
    width: 280px;
    right: 15px;
  }
  
  .notification-item {
    padding: 10px 12px;
  }
  
  .notification-icon {
    width: 32px;
    height: 32px;
    margin-right: 10px;
  }
  
  .notification-icon i {
    font-size: 14px;
  }
  
  .notification-title {
    font-size: 13px;
  }
  
  .notification-message {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .notification-dropdown {
    width: 250px;
    right: 10px;
  }
  
  .notification-title-bar {
    padding: 12px 12px 5px;
  }
  
  .notification-title-bar h3 {
    font-size: 15px;
  }
  
  .notification-item {
    padding: 8px 10px;
  }
  
  .notification-footer {
    padding: 8px 12px;
  }
}
