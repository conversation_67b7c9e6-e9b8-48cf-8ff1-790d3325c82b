// ========================================
// SISTEMA DE AUTENTICACIÓN AVISOFT
// ========================================

// Configuración y variables globales
const AUTH_CONFIG = {
    minPasswordLength: 8,
    sessionDuration: 24 * 60 * 60 * 1000, // 24 horas en milisegundos
    rememberDuration: 30 * 24 * 60 * 60 * 1000 // 30 días en milisegundos
};

// Base de datos simulada de usuarios (en producción sería una API)
let users = JSON.parse(localStorage.getItem('avisoft_users')) || [];

// Usuario actual
let currentUser = JSON.parse(localStorage.getItem('avisoft_current_user')) || null;

// ========================================
// FUNCIONES DE UTILIDAD
// ========================================

// Mostrar notificación
function showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    const messageElement = document.getElementById('notificationMessage');
    const icon = notification.querySelector('i');
    
    messageElement.textContent = message;
    notification.className = `notification ${type}`;
    
    // Cambiar icono según el tipo
    if (type === 'error') {
        icon.className = 'fas fa-exclamation-circle';
    } else if (type === 'warning') {
        icon.className = 'fas fa-exclamation-triangle';
    } else {
        icon.className = 'fas fa-check-circle';
    }
    
    notification.classList.add('show');
    
    setTimeout(() => {
        notification.classList.remove('show');
    }, 4000);
}

// Validar email
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Validar contraseña
function validatePassword(password) {
    const minLength = password.length >= AUTH_CONFIG.minPasswordLength;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    return {
        isValid: minLength && hasUpperCase && hasLowerCase && hasNumbers,
        strength: calculatePasswordStrength(password),
        requirements: {
            minLength,
            hasUpperCase,
            hasLowerCase,
            hasNumbers,
            hasSpecialChar
        }
    };
}

// Calcular fortaleza de contraseña
function calculatePasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    
    if (score < 3) return 'weak';
    if (score < 4) return 'fair';
    if (score < 6) return 'good';
    return 'strong';
}

// Mostrar error en campo
function showFieldError(fieldId, message) {
    const errorElement = document.getElementById(fieldId + 'Error');
    const field = document.getElementById(fieldId);
    
    if (errorElement && field) {
        errorElement.textContent = message;
        errorElement.classList.add('show');
        field.style.borderColor = '#f44336';
    }
}

// Limpiar error en campo
function clearFieldError(fieldId) {
    const errorElement = document.getElementById(fieldId + 'Error');
    const field = document.getElementById(fieldId);
    
    if (errorElement && field) {
        errorElement.classList.remove('show');
        field.style.borderColor = '#e0e0e0';
    }
}

// Generar ID único
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// ========================================
// FUNCIONES DE AUTENTICACIÓN
// ========================================

// Registrar usuario
function registerUser(userData) {
    // Verificar si el email ya existe
    const existingUser = users.find(user => user.email === userData.email);
    if (existingUser) {
        throw new Error('Ya existe una cuenta con este correo electrónico');
    }
    
    // Crear nuevo usuario
    const newUser = {
        id: generateId(),
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        phone: userData.phone || '',
        farmName: userData.farmName || '',
        password: userData.password, // En producción, esto debería estar hasheado
        createdAt: new Date().toISOString(),
        isActive: true,
        acceptedTerms: userData.acceptTerms,
        newsletter: userData.acceptNewsletter || false
    };
    
    // Agregar a la base de datos
    users.push(newUser);
    localStorage.setItem('avisoft_users', JSON.stringify(users));
    
    return newUser;
}

// Iniciar sesión
function loginUser(email, password, rememberMe = false) {
    const user = users.find(u => u.email === email && u.password === password);
    
    if (!user) {
        throw new Error('Credenciales incorrectas');
    }
    
    if (!user.isActive) {
        throw new Error('Esta cuenta ha sido desactivada');
    }
    
    // Crear sesión
    const session = {
        user: {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            farmName: user.farmName
        },
        loginTime: new Date().toISOString(),
        expiresAt: new Date(Date.now() + (rememberMe ? AUTH_CONFIG.rememberDuration : AUTH_CONFIG.sessionDuration)).toISOString(),
        rememberMe: rememberMe
    };
    
    // Guardar sesión
    localStorage.setItem('avisoft_current_user', JSON.stringify(session));
    currentUser = session;
    
    return session;
}

// Cerrar sesión
function logoutUser() {
    localStorage.removeItem('avisoft_current_user');
    currentUser = null;
    window.location.href = 'index.html';
}

// Verificar si el usuario está autenticado
function isAuthenticated() {
    if (!currentUser) return false;
    
    const now = new Date();
    const expiresAt = new Date(currentUser.expiresAt);
    
    if (now > expiresAt) {
        logoutUser();
        return false;
    }
    
    return true;
}

// ========================================
// INICIALIZACIÓN Y EVENTOS
// ========================================

document.addEventListener('DOMContentLoaded', function() {
    // Verificar si estamos en página de login
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        initializeLoginPage();
    }
    
    // Verificar si estamos en página de registro
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        initializeRegisterPage();
    }
    
    // Verificar autenticación en páginas protegidas
    checkAuthenticationStatus();
});

// Inicializar página de login
function initializeLoginPage() {
    const loginForm = document.getElementById('loginForm');
    const togglePassword = document.getElementById('togglePassword');
    
    // Toggle de contraseña
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                icon.className = 'fas fa-eye';
            }
        });
    }
    
    // Manejar envío del formulario
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;
        
        // Limpiar errores previos
        clearFieldError('email');
        clearFieldError('password');
        
        // Validaciones
        let hasErrors = false;
        
        if (!email) {
            showFieldError('email', 'El correo electrónico es requerido');
            hasErrors = true;
        } else if (!validateEmail(email)) {
            showFieldError('email', 'Ingresa un correo electrónico válido');
            hasErrors = true;
        }
        
        if (!password) {
            showFieldError('password', 'La contraseña es requerida');
            hasErrors = true;
        }
        
        if (hasErrors) return;
        
        // Intentar iniciar sesión
        try {
            const session = loginUser(email, password, rememberMe);
            showNotification('¡Bienvenido de vuelta!', 'success');
            
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
            
        } catch (error) {
            showNotification(error.message, 'error');
        }
    });
}

// Inicializar página de registro
function initializeRegisterPage() {
    const registerForm = document.getElementById('registerForm');
    const togglePassword = document.getElementById('togglePassword');
    const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
    const passwordField = document.getElementById('password');
    
    // Toggle de contraseñas
    [togglePassword, toggleConfirmPassword].forEach(toggle => {
        if (toggle) {
            toggle.addEventListener('click', function() {
                const targetId = this.id === 'togglePassword' ? 'password' : 'confirmPassword';
                const targetField = document.getElementById(targetId);
                const icon = this.querySelector('i');
                
                if (targetField.type === 'password') {
                    targetField.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                } else {
                    targetField.type = 'password';
                    icon.className = 'fas fa-eye';
                }
            });
        }
    });
    
    // Indicador de fortaleza de contraseña
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            const password = this.value;
            const strengthIndicator = document.getElementById('passwordStrength');
            const strengthFill = strengthIndicator.querySelector('.strength-fill');
            const strengthText = strengthIndicator.querySelector('.strength-text');
            
            if (password.length === 0) {
                strengthFill.className = 'strength-fill';
                strengthText.textContent = 'Fortaleza de la contraseña';
                return;
            }
            
            const strength = calculatePasswordStrength(password);
            strengthFill.className = `strength-fill ${strength}`;
            
            const strengthTexts = {
                weak: 'Débil - Agrega más caracteres',
                fair: 'Regular - Agrega números y símbolos',
                good: 'Buena - Casi perfecta',
                strong: 'Excelente - Muy segura'
            };
            
            strengthText.textContent = strengthTexts[strength];
        });
    }
    
    // Manejar envío del formulario
    registerForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            firstName: document.getElementById('firstName').value.trim(),
            lastName: document.getElementById('lastName').value.trim(),
            email: document.getElementById('email').value.trim(),
            phone: document.getElementById('phone').value.trim(),
            farmName: document.getElementById('farmName').value.trim(),
            password: document.getElementById('password').value,
            confirmPassword: document.getElementById('confirmPassword').value,
            acceptTerms: document.getElementById('acceptTerms').checked,
            acceptNewsletter: document.getElementById('acceptNewsletter').checked
        };
        
        // Limpiar errores previos
        Object.keys(formData).forEach(key => {
            if (key !== 'acceptTerms' && key !== 'acceptNewsletter') {
                clearFieldError(key);
            }
        });
        
        // Validaciones
        let hasErrors = false;
        
        if (!formData.firstName) {
            showFieldError('firstName', 'El nombre es requerido');
            hasErrors = true;
        }
        
        if (!formData.lastName) {
            showFieldError('lastName', 'El apellido es requerido');
            hasErrors = true;
        }
        
        if (!formData.email) {
            showFieldError('email', 'El correo electrónico es requerido');
            hasErrors = true;
        } else if (!validateEmail(formData.email)) {
            showFieldError('email', 'Ingresa un correo electrónico válido');
            hasErrors = true;
        }
        
        const passwordValidation = validatePassword(formData.password);
        if (!passwordValidation.isValid) {
            showFieldError('password', 'La contraseña debe tener al menos 8 caracteres, mayúsculas, minúsculas y números');
            hasErrors = true;
        }
        
        if (formData.password !== formData.confirmPassword) {
            showFieldError('confirmPassword', 'Las contraseñas no coinciden');
            hasErrors = true;
        }
        
        if (!formData.acceptTerms) {
            showNotification('Debes aceptar los términos y condiciones', 'error');
            hasErrors = true;
        }
        
        if (hasErrors) return;
        
        // Intentar registrar usuario
        try {
            const newUser = registerUser(formData);
            showNotification('¡Cuenta creada exitosamente! Redirigiendo...', 'success');
            
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
            
        } catch (error) {
            showNotification(error.message, 'error');
        }
    });
}

// Verificar estado de autenticación
function checkAuthenticationStatus() {
    // Si estamos en páginas de auth y ya estamos autenticados, redirigir
    const currentPage = window.location.pathname;
    if ((currentPage.includes('login.html') || currentPage.includes('registro.html')) && isAuthenticated()) {
        window.location.href = 'index.html';
    }
}

// Exportar funciones para uso global
window.AuthSystem = {
    isAuthenticated,
    getCurrentUser: () => currentUser,
    logout: logoutUser,
    showNotification
};
