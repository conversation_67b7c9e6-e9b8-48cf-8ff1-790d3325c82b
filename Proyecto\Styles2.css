* {
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', sans-serif;
    margin: 0;
    padding: 0;
  }
  body {
    display: flex;
    min-height: 100vh;
    background: #fff;
  }
  aside {
    width: 220px;
    background: #f3fdf1;
    border-right: 2px solid #d3f0c2;
    padding: 20px;
  }
  .logo img {
    max-width: 100%;
    height: auto;
  }
  .menu a {
    display: block;
    margin: 10px 0;
    padding: 10px;
    background: #e0f7d9;
    text-decoration: none;
    color: #000;
    border-radius: 6px;
  }
  .menu a:hover {
    background: #c8f0bb;
  }
  main {
    flex: 1;
    padding: 30px;
  }
  h2 {
    color: #319a11;
    margin-bottom: 20px;
  }
  .data-box {
    border: 2px solid #4CAF50;
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 10px;
  }
  .row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
  }
  .column {
    flex: 1;
  }
  footer {
    text-align: center;
    padding: 10px;
    color: gray;
  }