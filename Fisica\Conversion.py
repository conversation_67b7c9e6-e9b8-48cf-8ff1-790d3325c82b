def convertir_longitud(valor, unidad_origen, unidad_destino):
    # Conversiones de Longitud
    conversiones_longitud = {
        'm': 1,       # metro
        'cm': 100,    # centímetro
        'mm': 1000,   # milímetro
        'km': 0.001,  # kilómetro
        'mi': 0.000621371, # milla
        'yd': 1.09361,   # yarda
        'ft': 3.28084    # pie
    }

    # Conversión entre unidades
    if unidad_origen in conversiones_longitud and unidad_destino in conversiones_longitud:
        valor_convertido = valor * (conversiones_longitud[unidad_destino] / conversiones_longitud[unidad_origen])
        return valor_convertido
    else:
        return "Unidad no válida"

def convertir_masa(valor, unidad_origen, unidad_destino):
    # Conversiones de Masa
    conversiones_masa = {
        'kg': 1,        # kilogramo
        'g': 1000,      # gramo
        'mg': 1000000,  # miligramo
        't': 0.001,     # tonelada
        'lb': 2.20462,  # libra
        'oz': 35.274    # onza
    }

    # Conversión entre unidades
    if unidad_origen in conversiones_masa and unidad_destino in conversiones_masa:
        valor_convertido = valor * (conversiones_masa[unidad_destino] / conversiones_masa[unidad_origen])
        return valor_convertido
    else:
        return "Unidad no válida"

def convertir_tiempo(valor, unidad_origen, unidad_destino):
    # Conversiones de Tiempo
    conversiones_tiempo = {
        's': 1,       # segundo
        'min': 1/60,  # minuto
        'h': 1/3600,  # hora
        'd': 1/86400  # día
    }

    # Conversión entre unidades
    if unidad_origen in conversiones_tiempo and unidad_destino in conversiones_tiempo:
        valor_convertido = valor * (conversiones_tiempo[unidad_destino] / conversiones_tiempo[unidad_origen])
        return valor_convertido
    else:
        return "Unidad no válida"

def convertir_corriente(valor, unidad_origen, unidad_destino):
    # Conversiones de Corriente Eléctrica
    conversiones_corriente = {
        'A': 1,       # amperio
        'mA': 1000,   # miliamperio
        'uA': 1000000 # microamperio
    }

    # Conversión entre unidades
    if unidad_origen in conversiones_corriente and unidad_destino in conversiones_corriente:
        valor_convertido = valor * (conversiones_corriente[unidad_destino] / conversiones_corriente[unidad_origen])
        return valor_convertido
    else:
        return "Unidad no válida"

def convertir_temperatura(valor, unidad_origen, unidad_destino):
    # Conversiones de Temperatura
    if unidad_origen == 'C' and unidad_destino == 'K':
        return valor + 273.15
    elif unidad_origen == 'K' and unidad_destino == 'C':
        return valor - 273.15
    elif unidad_origen == 'C' and unidad_destino == 'F':
        return valor * 9/5 + 32
    elif unidad_origen == 'F' and unidad_destino == 'C':
        return (valor - 32) * 5/9
    elif unidad_origen == 'K' and unidad_destino == 'F':
        return (valor - 273.15) * 9/5 + 32
    elif unidad_origen == 'F' and unidad_destino == 'K':
        return (valor - 32) * 5/9 + 273.15
    else:
        return "Unidad no válida"

def convertir_cantidad_sustancia(valor, unidad_origen, unidad_destino):
    # Conversiones de Cantidad de Sustancia (mol)
    if unidad_origen == 'mol' and unidad_destino == 'mol':
        return valor
    else:
        return "Unidad no válida"

def convertir_intensidad_luminosa(valor, unidad_origen, unidad_destino):
    # Conversiones de Intensidad Luminosa (candela)
    if unidad_origen == 'cd' and unidad_destino == 'cd':
        return valor
    else:
        return "Unidad no válida"

    
def main():
    while True:
        print("\nBienvenido al conversor de unidades físicas.")
        print("1. Longitud")
        print("2. Masa")
        print("3. Tiempo")
        print("4. Intensidad de corriente eléctrica")
        print("5. Temperatura termodinámica")
        print("6. Cantidad de sustancia")
        print("7. Intensidad luminosa")
        print("8. Salir")

        opcion = int(input("Elige el tipo de magnitud física que deseas convertir (1-8): "))

        if opcion == 8:
            print("Gracias por usar el conversor. ¡Hasta luego!")
            break  # Termina el bucle y el programa

        if opcion == 1:
            valor = float(input("Introduce el valor a convertir: "))
            unidad_origen = input("Introduce la unidad de origen (m, cm, mm, km, mi, yd, ft): ").strip()
            unidad_destino = input("Introduce la unidad de destino (m, cm, mm, km, mi, yd, ft): ").strip()
            resultado = convertir_longitud(valor, unidad_origen, unidad_destino)

        elif opcion == 2:
            valor = float(input("Introduce el valor a convertir: "))
            unidad_origen = input("Introduce la unidad de origen (kg, g, mg, t, lb, oz): ").strip()
            unidad_destino = input("Introduce la unidad de destino (kg, g, mg, t, lb, oz): ").strip()
            resultado = convertir_masa(valor, unidad_origen, unidad_destino)

        elif opcion == 3:
            valor = float(input("Introduce el valor a convertir: "))
            unidad_origen = input("Introduce la unidad de origen (s, min, h, d): ").strip()
            unidad_destino = input("Introduce la unidad de destino (s, min, h, d): ").strip()
            resultado = convertir_tiempo(valor, unidad_origen, unidad_destino)

        elif opcion == 4:
            valor = float(input("Introduce el valor a convertir: "))
            unidad_origen = input("Introduce la unidad de origen (A, mA, uA): ").strip()
            unidad_destino = input("Introduce la unidad de destino (A, mA, uA): ").strip()
            resultado = convertir_corriente(valor, unidad_origen, unidad_destino)

        elif opcion == 5:
            valor = float(input("Introduce el valor a convertir: "))
            unidad_origen = input("Introduce la unidad de origen (C, K, F): ").strip()
            unidad_destino = input("Introduce la unidad de destino (C, K, F): ").strip()
            resultado = convertir_temperatura(valor, unidad_origen, unidad_destino)

        elif opcion == 6:
            valor = float(input("Introduce el valor a convertir: "))
            unidad_origen = input("Introduce la unidad de origen (mol): ").strip()
            unidad_destino = input("Introduce la unidad de destino (mol): ").strip()
            resultado = convertir_cantidad_sustancia(valor, unidad_origen, unidad_destino)

        elif opcion == 7:
            valor = float(input("Introduce el valor a convertir: "))
            unidad_origen = input("Introduce la unidad de origen (cd): ").strip()
            unidad_destino = input("Introduce la unidad de destino (cd): ").strip()
            resultado = convertir_intensidad_luminosa(valor, unidad_origen, unidad_destino)

        else:
            print("Opción no válida.")
            continue  # Vuelve al inicio del bucle

        print(f"El valor convertido es: {resultado}")

        repetir = input("¿Quieres hacer otra conversión? (s/n): ").strip().lower()
        if repetir != 's':
            print("ok..")
            break  # Sale del bucle y finaliza el programa
        


if __name__ == "__main__":
    main()

