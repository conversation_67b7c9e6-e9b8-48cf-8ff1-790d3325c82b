* {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      font-family: '<PERSON>o', sans-serif;
    }

    body {
      background-color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      min-height: 100vh;
    }

    header {
      width: 100%;
      background: linear-gradient(135deg, #43a411, #2e7d32);
      display: flex;
      align-items: center;
      padding: 20px;
      color: white;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    header img {
      width: 80px;
      height: auto;
      margin-right: 20px;
      filter: drop-shadow(0 2px 5px rgba(0,0,0,0.2));
      transition: transform 0.3s ease;
    }

    header img:hover {
      transform: scale(1.05);
    }

    header h1 {
      font-size: 28px;
      color: white;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    }

    .container {
      margin-top: 50px;
      background-color: white;
      padding: 40px 30px;
      border-radius: 15px;
      width: 90%;
      max-width: 400px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      text-align: center;
      border: 1px solid #eee;
    }

    .container h2 {
      margin-bottom: 30px;
      font-size: 20px;
      font-weight: bold;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .input-icon {
      position: relative;
      display: flex;
      align-items: center;
    }

    .input-icon i {
      position: absolute;
      left: 12px;
      color: #43a411;
      font-size: 16px;
    }

    input[type="email"],
    input[type="password"] {
      width: 100%;
      padding: 12px 12px 12px 40px;
      border: 1px solid #ddd;
      border-radius: 8px;
      background-color: #f9f9f9;
      color: rgb(0, 0, 0);
      font-size: 14px;
      transition: all 0.3s ease;
    }

    input[type="email"]:focus,
    input[type="password"]:focus {
      border-color: #43a411;
      box-shadow: 0 0 0 2px rgba(67, 164, 17, 0.2);
      outline: none;
    }

    input::placeholder {
      color: #333;
      font-style: italic;
    }

    .login-btn {
      margin-top: 15px;
      width: 100%;
      padding: 12px;
      background: linear-gradient(135deg, #5cae36, #43a411);
      color: white;
      font-weight: bold;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }

    .login-btn:hover {
      background: linear-gradient(135deg, #43a411, #2e7d32);
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .forgot {
      margin-top: 20px;
      font-size: 13px;
    }

    .forgot a {
      color: #43a411;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 5px;
      transition: color 0.3s ease;
    }

    .forgot a:hover {
      color: #2e7d32;
      text-decoration: underline;
    }

    footer {
      margin-top: 40px;
      color: #555;
      font-size: 12px;
      padding: 10px;
      text-align: center;
    }