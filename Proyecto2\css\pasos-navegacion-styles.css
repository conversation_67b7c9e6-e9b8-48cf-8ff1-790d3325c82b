/* ========================================
   ESTILOS PARA PASOS Y NAVEGACIÓN
   ======================================== */

/* Estilos para los pasos mejorados */
.steps-container {
  margin: 30px 0;
}

.steps-container.vertical {
  display: flex;
  flex-direction: column;
}

.step-item {
  display: flex;
  margin-bottom: 25px;
  position: relative;
}

.step-item.enhanced {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.step-item.enhanced:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.step-number {
  width: 40px;
  height: 40px;
  background-color: #4caf50;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  margin-right: 20px;
  flex-shrink: 0;
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.step-content {
  flex: 1;
  position: relative;
}

.step-content h3 {
  color: #2e7d32;
  margin-bottom: 8px;
  font-size: 1.35rem;
}

.step-content p {
  margin-bottom: 0;
  font-size: 1.15rem;
}

.step-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 2rem;
  color: rgba(76, 175, 80, 0.15);
}

.step-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
  border-top: 1px dashed #e0e0e0;
  padding-top: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 6px;
  flex: 1;
  min-width: 150px;
}

.detail-item i {
  font-size: 1rem;
  color: #4caf50;
  margin-right: 8px;
}

/* Imagen de uso con overlay */
.usage-overview {
  margin: 30px 0;
}

.usage-image {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.usage-image img {
  width: 100%;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Estilos para beneficios destacados */
.benefits-highlight {
  margin: 30px 0;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.benefits-highlight.vertical {
  margin-top: 30px;
}

.benefits-highlight h4 {
  color: #2e7d32;
  font-size: 1.6rem;
  text-align: center;
  margin-bottom: 25px;
  font-weight: 600;
}

.benefits-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.benefits-container.vertical {
  display: flex;
  flex-direction: column;
}

.benefits-container.vertical .benefit-item {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}

.benefit-item {
  flex: 1;
  min-width: 250px;
  background: #f8f9fa;
  padding: 25px;
  border-radius: 10px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-left: 4px solid #4caf50;
}

.benefit-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  background: white;
}

.benefit-item i {
  font-size: 2.5rem;
  color: #4caf50;
  margin-bottom: 15px;
}

.benefit-item h3 {
  color: #2e7d32;
  margin-bottom: 10px;
  font-size: 1.3rem;
}

.benefit-item p {
  color: #666;
  line-height: 1.5;
}

.benefit-metric {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: bold;
  margin-top: 10px;
  display: inline-block;
}

/* Iconos de beneficios mejorados */
.benefits-icons {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.benefits-icons.vertical {
  display: flex;
  flex-direction: column;
}

.benefits-icons.vertical .benefit-icon {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  text-align: left;
}

.benefits-icons.vertical .benefit-icon .icon-circle {
  margin-right: 20px;
  margin-bottom: 0;
}

.benefits-icons.vertical .benefit-icon span {
  font-weight: bold;
  font-size: 1.1rem;
  color: #2e7d32;
}

.benefits-icons.vertical .benefit-detail {
  margin-left: 80px;
  margin-top: -20px;
}

.benefit-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
  min-width: 100px;
}

.icon-circle {
  width: 60px;
  height: 60px;
  background-color: #e8f5e9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.benefit-icon:hover .icon-circle {
  background-color: #4caf50;
}

.benefit-icon:hover .icon-circle i {
  color: white;
}

.icon-circle i {
  font-size: 2rem;
  color: #4caf50;
}

.benefit-icon span {
  color: #555;
  font-weight: 500;
}

.benefit-detail {
  font-size: 0.9rem;
  color: #666;
  margin-top: 5px;
}

/* Testimonial */
.testimonial {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  margin: 20px 0;
  position: relative;
  border-left: 4px solid #4caf50;
}

.testimonial-quote {
  position: relative;
  padding-left: 40px;
}

.testimonial-quote i {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 1.5rem;
  color: #4caf50;
  opacity: 0.5;
}

.testimonial-quote p {
  font-style: italic;
  color: #555;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.testimonial-author {
  text-align: right;
}

.testimonial-author strong {
  display: block;
  color: #2e7d32;
}

.testimonial-author span {
  font-size: 0.9rem;
  color: #666;
}

/* Botones de CTA */
.cta-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 30px;
}

.cta-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  align-self: flex-start;
  box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
}

.cta-button i {
  margin-right: 10px;
}

.cta-button:hover {
  background-color: #388e3c;
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.4);
}

.cta-button.primary {
  background-color: #4caf50;
  color: white;
}

.cta-button.secondary {
  background-color: #f1f8e9;
  color: #2e7d32;
  border: 1px solid #4caf50;
}

.cta-button.secondary:hover {
  background-color: #e8f5e9;
}

/* Responsive para pasos y navegación */
@media (max-width: 768px) {
  .step-item {
    flex-direction: column;
  }
  
  .step-number {
    margin-right: 0;
    margin-bottom: 15px;
    align-self: center;
  }
  
  .step-content {
    text-align: center;
  }
  
  .step-icon {
    position: static;
    transform: none;
    margin-top: 15px;
    text-align: center;
  }
  
  .step-details {
    flex-direction: column;
  }
  
  .detail-item {
    min-width: auto;
  }
  
  .benefits-container {
    flex-direction: column;
  }
  
  .benefit-item {
    min-width: auto;
  }
  
  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-button {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .step-item.enhanced {
    padding: 15px;
  }
  
  .step-content h3 {
    font-size: 1.2rem;
  }
  
  .step-content p {
    font-size: 1rem;
  }
  
  .benefits-icons {
    flex-direction: column;
    align-items: center;
  }
  
  .benefit-icon {
    min-width: auto;
    margin-bottom: 20px;
  }
}
