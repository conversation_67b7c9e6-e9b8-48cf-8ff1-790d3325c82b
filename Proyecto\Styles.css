/* Estilos generales */
* {
box-sizing: border-box;
font-family: 'Roboto', sans-serif;
margin: 0;
padding: 0;
}
body {
display: flex;
min-height: 100vh;
background: #fff;
}
aside {
  position: fixed;
  top: 0;
  left: 0;
  width: 250px;
  height: 100%;
  background: linear-gradient(135deg, #09ae00, #2e7d32);
  color: rgb(255, 255, 255);
  padding-top: 20px;
  box-shadow: 3px 0 15px rgba(0,0,0,0.2);
  z-index: 100;
}

aside h1 {
text-align: center;
margin-bottom: 30px;
}

.logo {
  text-align: center;
  padding: 10px 0 25px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  margin-bottom: 20px;
}

.logo img {
  max-width: 60%;
  height: auto;
  filter: drop-shadow(0 2px 5px rgba(0,0,0,0.2));
  transition: transform 0.3s ease;
}

.logo img:hover {
  transform: scale(1.05);
}
.menu {
  padding: 0 15px;
}

.menu a {
  display: flex;
  align-items: center;
  margin: 8px 0;
  padding: 12px 15px;
  background: rgba(255,255,255,0.1);
  text-decoration: none;
  color: #fff;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.menu a i {
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.menu a:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: rgba(255,255,255,0.1);
  transition: width 0.3s ease;
  z-index: -1;
}

.menu a:hover {
  background: rgba(255,255,255,0.2);
  transform: translateX(5px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.menu a:hover:before {
  width: 100%;
}
main {
  margin-left: 280px;
  padding: 30px;
  font-family: 'Roboto', sans-serif;
  max-width: calc(100% - 280px);
  position: relative;
}

/* Top Buttons Styles */
.top-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  position: absolute;
  top: 10px;
  right: 20px;
  z-index: 10;
}

.top-button {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 500;
  font-size: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top-button i {
  margin-right: 5px;
  font-size: 14px;
}

.login-button {
  background-color: white;
  color: #4CAF50;
  border: 2px solid #4CAF50;
}

.login-button:hover {
  background-color: #f0f9f0;
  transform: translateY(-3px);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1);
}

.subscribe-button {
  background-color: #4CAF50;
  color: white;
  border: 2px solid #4CAF50;
}

.subscribe-button:hover {
  background-color: #3d9c3d;
  transform: translateY(-3px);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1);
}
.header {
font-size: 22px;
font-weight: bold;
margin-bottom: 20px;
margin-top: 30px;
}
.info-section {
display: flex;
gap: 20px;
}
.info-box, .benefits-box {
flex: 1;
padding: 20px;
border: 2px solid #62b420;
border-radius: 12px;
}
.info-box h2, .benefits-box h2 {
color: #2b9a06;
font-size: 18px;
margin-bottom: 10px;
}
.info-box p, .benefits-box ul {
font-size: 16px;
font-weight: bold;
}
.benefits-box ul {
list-style-type: '✅';
padding-left: 20px;
}
.image-section {
margin: 20px 0;
text-align: center;
}
.image-section img {
max-width: 100%;
border-radius: 16px;
border: 4px solid #d0d0d0;
}
.cta {
text-align: center;
margin: 30px 0;
}
.cta button {
background: #41bd18;
color: white;
padding: 10px 20px;
border: none;
border-radius: 8px;
font-size: 16px;
cursor: pointer;
}
.cta button:hover {
background: #319a11;
}

table {
width: 100%;
margin-top: 20px;
border-collapse: collapse;
}
th, td {
border: 1px solid #ddd;
padding: 8px;
text-align: center;
}
th {
background-color: #e6f4e6;
color: #000;
}
.menu a.active {
    background-color: rgba(255,255,255,0.25);
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
    border-left: 4px solid #fff;
}
h2 {
    color: #319a11;
    margin-bottom: 20px;
}
/* Estilos para la página de Monitoreo */
.dashboard-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.dashboard-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.dashboard-card.history {
  grid-column: span 2;
}

.card-header {
  background-color: #4CAF50;
  color: white;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  display: flex;
  align-items: center;
}

.card-header h3 i {
  margin-right: 10px;
}

.card-body {
  padding: 20px;
}

/* Estilos para el panel de estado del sistema */
.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: bold;
  color: #555;
}

.status-value {
  padding: 5px 10px;
  border-radius: 20px;
  background-color: #f0f0f0;
  font-size: 14px;
}

.status-value.online {
  background-color: #e6f7e6;
  color: #2e7d32;
}

/* Estilos para los paneles de temperatura y humedad */
.current-reading {
  text-align: center;
  margin-bottom: 15px;
}

.reading-value {
  font-size: 48px;
  font-weight: bold;
  color: #333;
}

.reading-unit {
  font-size: 24px;
  color: #666;
  margin-left: 5px;
}

.reading-chart {
  height: 120px;
  background-color: #f9f9f9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  border: 1px dashed #ddd;
}

.chart-placeholder {
  color: #999;
  font-style: italic;
}

.reading-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: #777;
  margin-bottom: 5px;
}

.stat-value {
  font-weight: bold;
  color: #333;
}

/* Estilos para la tabla de datos */
.data-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

.data-table th {
  background-color: #f2f2f2;
  color: #333;
  font-weight: bold;
  text-align: left;
  padding: 12px 15px;
  border-bottom: 2px solid #ddd;
}

.data-table td {
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
}

.data-table tr:hover {
  background-color: #f9f9f9;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.status-badge.normal {
  background-color: #e6f7e6;
  color: #2e7d32;
}

.status-badge.warning {
  background-color: #fff3e0;
  color: #e65100;
}

.table-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.action-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.action-button:hover {
  background-color: #388e3c;
}

/* Estilos para la notificación de actualización */
.update-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #4CAF50;
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.update-notification.fade-out {
  animation: fadeOut 0.5s ease-in forwards;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Estilos para el contador de tiempo */
#last-update {
  transition: color 0.3s ease;
}

/* Estilos para dispositivos móviles */
@media (max-width: 768px) {
  .dashboard-container {
    grid-template-columns: 1fr;
  }

  .dashboard-card.history {
    grid-column: span 1;
  }
}

/* Estilos originales para compatibilidad */
.data-box {
  border: 3px solid #4CAF50;
  margin-bottom: 20px;
  padding: 40px;
  border-radius: 10px;
}

.row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.column {
  flex: 1;
}
  footer {
    text-align: center;
    padding: 10px;
    color: gray;
  }

  .Aside img {
    width: 60px;
    height: auto;
    margin-right: 15px;


  }



  /* Estilos para la página de Inicio */
.hero-section {
  display: flex;
  align-items: center;
  gap: 40px;
  padding: 40px 0;
  margin-bottom: 60px;
  margin-top: 20px;
}

.hero-content {
  flex: 1;
}

.hero-title {
  font-size: 2.5rem;
  color: #2e7d32;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #555;
  margin-bottom: 30px;
  line-height: 1.5;
}

.hero-buttons {
  display: flex;
  gap: 15px;
}

.primary-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primary-button:hover {
  background-color: #388e3c;
}

.secondary-button {
  background-color: white;
  color: #4CAF50;
  border: 2px solid #4CAF50;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondary-button:hover {
  background-color: #f1f8e9;
}

.hero-image {
  flex: 1;
  position: relative;
}

.hero-image img {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.section-title {
  text-align: center;
  color: #2e7d32;
  font-size: 2rem;
  margin-bottom: 40px;
  position: relative;
  padding-bottom: 15px;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #4CAF50;
}

.features-section {
  padding: 60px 0;
  background-color: #f9f9f9;
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 2.5rem;
  color: #4CAF50;
  margin-bottom: 20px;
}

.feature-card h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

.benefits-section {
  display: flex;
  align-items: center;
  gap: 40px;
  padding: 60px 0;
  margin-bottom: 60px;
}

.benefits-content {
  flex: 1;
}

.benefits-list {
  list-style: none;
  padding: 0;
}

.benefits-list li {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  line-height: 1.5;
}

.check-icon {
  color: #4CAF50;
  font-size: 1.2rem;
  margin-right: 10px;
  font-weight: bold;
}

.benefits-image {
  flex: 1;
}

.benefits-image img {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.testimonials-section {
  padding: 60px 0;
  background-color: #f9f9f9;
  margin-bottom: 60px;
}

.testimonials-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.testimonial-card {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.testimonial-content {
  margin-bottom: 20px;
}

.testimonial-content p {
  font-style: italic;
  color: #555;
  line-height: 1.6;
  position: relative;
  padding: 0 20px;
}

.testimonial-content p:before,
.testimonial-content p:after {
  content: '"';
  font-size: 1.5rem;
  color: #4CAF50;
  font-weight: bold;
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.author-info h4 {
  margin: 0;
  color: #333;
}

.author-info p {
  margin: 5px 0 0;
  color: #777;
  font-size: 0.9rem;
}

.cta-section {
  text-align: center;
  padding: 60px 0;
  background-color: #e8f5e9;
  margin-bottom: 60px;
  border-radius: 10px;
}

.cta-section h2 {
  color: #2e7d32;
  margin-bottom: 15px;
  font-size: 2rem;
}

.cta-section p {
  color: #555;
  margin-bottom: 30px;
  font-size: 1.1rem;
}

.registration-section {
  margin-bottom: 60px;
}

.registration-section h3 {
  color: #2e7d32;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.registration-table {
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  overflow: hidden;
}

.registration-table th,
.registration-table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.registration-table th {
  background-color: #4CAF50;
  color: white;
  font-weight: bold;
}

.registration-table tr:hover {
  background-color: #f9f9f9;
}

/* Footer Styles */
.main-footer {
  background-color: #222;
  color: white;
  padding: 70px 0 0;
  margin-top: 60px;
  position: relative;
}

.main-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #4CAF50, #2e7d32, #4CAF50);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto 40px;
  padding: 0 20px;
}

/* Footer Logo Section */
.footer-logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.footer-logo img {
  margin-bottom: 15px;
  filter: brightness(1.1);
  transition: transform 0.3s ease;
}

.footer-logo img:hover {
  transform: scale(1.05);
}

.footer-logo p {
  margin: 15px 0;
  color: #bbb;
  line-height: 1.6;
  font-size: 0.95rem;
}

.social-icons {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background-color: #4CAF50;
  transform: translateY(-3px);
}

/* Footer Links Section */
.footer-links h4,
.footer-contact h4,
.footer-newsletter h4 {
  color: white;
  margin-bottom: 25px;
  font-size: 1.2rem;
  position: relative;
  padding-bottom: 10px;
}

.footer-links h4::after,
.footer-contact h4::after,
.footer-newsletter h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: #4CAF50;
}

.footer-links ul {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: #bbb;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.footer-links a i {
  font-size: 12px;
  margin-right: 10px;
  color: #4CAF50;
  transition: transform 0.3s ease;
}

.footer-links a:hover {
  color: white;
  transform: translateX(5px);
}

.footer-links a:hover i {
  transform: translateX(3px);
}

/* Footer Contact Section */
.contact-info p {
  margin-bottom: 15px;
  color: #bbb;
  display: flex;
  align-items: flex-start;
  font-size: 0.95rem;
}

.contact-info i {
  margin-right: 15px;
  color: #4CAF50;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* Footer Newsletter Section */
.footer-newsletter p {
  color: #bbb;
  margin-bottom: 20px;
  line-height: 1.6;
  font-size: 0.95rem;
}

.newsletter-button {
  margin-top: 20px;
}

.subscribe-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  background-color: #4CAF50;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.subscribe-btn i {
  margin-right: 10px;
  font-size: 16px;
}

.subscribe-btn:hover {
  background-color: #3d9c3d;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Footer Bottom Section */
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 20px;
  background-color: #1a1a1a;
  margin-top: 50px;
  flex-wrap: wrap;
  gap: 20px;
}

.copyright {
  color: #999;
  font-size: 0.9rem;
}

.footer-bottom-links {
  display: flex;
  gap: 20px;
}

.footer-bottom-links a {
  color: #999;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: #4CAF50;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  /* Top buttons responsive styles */
  .top-buttons {
    position: relative;
    top: 0;
    right: 0;
    justify-content: flex-end;
    margin-bottom: 15px;
    width: 100%;
  }

  .top-button {
    font-size: 11px;
    padding: 5px 10px;
  }

  /* Footer responsive styles */
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .footer-logo {
    align-items: center;
  }

  .social-icons {
    justify-content: center;
  }

  .footer-links h4::after,
  .footer-contact h4::after,
  .footer-newsletter h4::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-links a {
    justify-content: center;
  }

  .contact-info p {
    justify-content: center;
  }

  .newsletter-button {
    display: flex;
    justify-content: center;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .footer-bottom-links {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .hero-section,
  .benefits-section {
    flex-direction: column;
  }

  .hero-image,
  .benefits-image {
    margin-top: 30px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .cta-section h2 {
    font-size: 1.8rem;
  }
}

/* Estilos para la página de Introducción */

  body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}
nav a {
    text-decoration: none;
    padding: 10px 20px;
    background-color: #70db70;
    color: white;
    border-radius: 5px;
}
.hero {
    text-align: center;
    padding: 40px 20px;
    background-color: #f5f5f5;
}
.hero h1 {
    color: #4CAF50;
}
.hero button {
    padding: 10px 20px;
    background-color: #70db70;
    border: none;
    color: white;
    border-radius: 5px;
    cursor: pointer;
}
.section {
    padding: 40px 20px;
    text-align: center;
}
.section img {
    max-width: 100%;
    height: auto;
}
.benefits {
    background-color: #f0f0f0;
    padding: 40px 20px;
}
.demo-btn {
    background-color: #70db70;
    padding: 10px 20px;
    border: none;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 20px;
}
.footer {
    background-color: #eee;
    padding: 20px;
    text-align: center;
}
/* Estilos para la sección final de la página de introducción */
.final-section {
    background-color: #f5f5f5;
    padding: 40px;
    border-radius: 10px;
    margin-top: 40px;
    margin-bottom: 40px;
    text-align: center;
}

.final-section .cta {
    max-width: 800px;
    margin: 0 auto;
}

.final-section h2 {
    color: #2e7d32;
    font-size: 2rem;
    margin-bottom: 20px;
}

.final-section p {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 30px;
    line-height: 1.6;
}

.final-section button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.final-section button:hover {
    background-color: #388e3c;
}

.intro-footer {
    text-align: center;
    padding: 20px;
    color: #777;
    border-top: 1px solid #eee;
    margin-top: 40px;
}

.intro-footer p {
    margin: 0;
    font-size: 0.9rem;
}
