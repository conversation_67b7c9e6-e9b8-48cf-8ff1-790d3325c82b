<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 Prueba de Conexión API AviSoft</h1>
    
    <h2>Pruebas Disponibles:</h2>
    <button onclick="testPHP()">1. Probar PHP</button>
    <button onclick="testRegisterAPI()">2. Probar API de Registro</button>
    <button onclick="testDatabase()">3. Probar Base de Datos</button>
    
    <div id="results"></div>

    <script>
        function addResult(message, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
        }

        async function testPHP() {
            try {
                addResult('🔄 Probando PHP...', true);
                const response = await fetch('test-php.php');
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ PHP funcionando: ${data.message} (Versión: ${data.php_version})`, true);
                } else {
                    addResult('❌ PHP no está funcionando correctamente', false);
                }
            } catch (error) {
                addResult(`❌ Error al probar PHP: ${error.message}`, false);
                addResult('💡 Sugerencia: Necesitas un servidor que soporte PHP (no Python)', false);
            }
        }

        async function testRegisterAPI() {
            try {
                addResult('🔄 Probando API de registro...', true);
                
                const testData = {
                    nombre: 'Test',
                    apellido: 'Usuario',
                    email: '<EMAIL>',
                    telefono: '1234567890',
                    password: 'Test123456',
                    confirm_password: 'Test123456',
                    acepto_terminos: true,
                    suscrito_newsletter: false
                };

                const response = await fetch('api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult(`✅ API de registro funcionando: ${data.message}`, true);
                } else {
                    addResult(`❌ Error en API de registro: ${data.message || 'Error desconocido'}`, false);
                }
            } catch (error) {
                addResult(`❌ Error al probar API de registro: ${error.message}`, false);
            }
        }

        async function testDatabase() {
            try {
                addResult('🔄 Probando conexión a base de datos...', true);
                
                // Intentar hacer una petición que requiera base de datos
                const response = await fetch('api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        nombre: '',
                        apellido: '',
                        email: '',
                        password: ''
                    })
                });

                const data = await response.json();
                
                if (response.status === 400 && data.message) {
                    addResult('✅ Conexión a base de datos OK (API responde con validaciones)', true);
                } else if (response.status === 500) {
                    addResult('❌ Error de base de datos o servidor', false);
                } else {
                    addResult(`🔍 Respuesta inesperada: ${data.message}`, false);
                }
            } catch (error) {
                addResult(`❌ Error al probar base de datos: ${error.message}`, false);
            }
        }

        // Información del entorno
        addResult(`🌐 Navegador: ${navigator.userAgent}`, true);
        addResult(`📍 URL actual: ${window.location.href}`, true);
    </script>
</body>
</html>
