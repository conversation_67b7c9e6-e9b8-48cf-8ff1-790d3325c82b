// ========================================
// FUNCIONALIDAD DE PESTAÑA DE PERFIL
// ========================================

// Variables globales
let profileTabOpen = false;

// ========================================
// FUNCIONES PRINCIPALES
// ========================================

// Inicializar pestaña de perfil
function initializeProfileTab() {
    console.log('Inicializando pestaña de perfil');
    
    const profileTabHeader = document.getElementById('profileTabHeader');
    const profileTabContent = document.getElementById('profileTabContent');
    const profileTabArrow = document.getElementById('profileTabArrow');
    
    if (!profileTabHeader || !profileTabContent) {
        console.log('Elementos de pestaña de perfil no encontrados');
        return;
    }
    
    // Evento para abrir/cerrar pestaña
    profileTabHeader.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleProfileTab();
    });
    
    // Cerrar pestaña al hacer clic fuera
    document.addEventListener('click', function(e) {
        if (profileTabOpen && !e.target.closest('.profile-tab')) {
            closeProfileTab();
        }
    });
    
    // Cerrar con tecla Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && profileTabOpen) {
            closeProfileTab();
        }
    });
    
    // Actualizar contenido inicial
    updateProfileTabContent();
    
    console.log('Pestaña de perfil inicializada');
}

// Alternar estado de la pestaña
function toggleProfileTab() {
    if (profileTabOpen) {
        closeProfileTab();
    } else {
        openProfileTab();
    }
}

// Abrir pestaña de perfil
function openProfileTab() {
    const profileTabContent = document.getElementById('profileTabContent');
    const profileTabArrow = document.getElementById('profileTabArrow');

    if (profileTabContent) {
        profileTabContent.classList.add('open');
        profileTabContent.classList.remove('closing');
        profileTabContent.classList.add('opening');

        setTimeout(() => {
            profileTabContent.classList.remove('opening');
        }, 300);
    }

    if (profileTabArrow) {
        profileTabArrow.classList.add('rotated');
    }

    profileTabOpen = true;
    console.log('Pestaña de perfil abierta');

    // Actualizar contenido al abrir
    updateProfileTabContent();
}

// Cerrar pestaña de perfil
function closeProfileTab() {
    const profileTabContent = document.getElementById('profileTabContent');
    const profileTabArrow = document.getElementById('profileTabArrow');

    if (profileTabContent) {
        profileTabContent.classList.remove('open');
        profileTabContent.classList.remove('opening');
        profileTabContent.classList.add('closing');

        setTimeout(() => {
            profileTabContent.classList.remove('closing');
        }, 300);
    }

    if (profileTabArrow) {
        profileTabArrow.classList.remove('rotated');
    }

    profileTabOpen = false;
    console.log('Pestaña de perfil cerrada');
}

// Actualizar contenido de la pestaña según estado de autenticación
function updateProfileTabContent() {
    const profileTab = document.getElementById('profileTab');
    const profileLogged = document.getElementById('profileLogged');

    if (!profileTab || !profileLogged) {
        console.log('Elementos de contenido de perfil no encontrados');
        return;
    }

    // Verificar si hay usuario logueado
    let isLoggedIn = false;
    let currentUser = null;

    // Verificar con sistema MySQL
    if (window.AuthMySQL && window.AuthMySQL.isLoggedIn()) {
        isLoggedIn = true;
        currentUser = window.AuthMySQL.getCurrentUser();
        console.log('Usuario MySQL detectado:', currentUser);
    }
    // Fallback al sistema original
    else if (window.AuthSystem && window.AuthSystem.isAuthenticated()) {
        isLoggedIn = true;
        const authData = window.AuthSystem.getCurrentUser();
        currentUser = authData.user;
        console.log('Usuario sistema original detectado:', currentUser);
    }

    if (isLoggedIn && currentUser) {
        // Mostrar botón de perfil
        profileTab.style.display = 'inline-block';
        profileTab.classList.remove('not-logged');

        // Actualizar información del usuario
        updateUserProfileInfo(currentUser);

        console.log('Mostrando botón de perfil de usuario logueado');
    } else {
        // Ocultar botón de perfil
        profileTab.style.display = 'none';
        profileTab.classList.add('not-logged');

        // Cerrar dropdown si está abierto
        closeProfileTab();

        console.log('Ocultando botón de perfil - usuario no logueado');
    }
}

// Actualizar información del usuario en la pestaña
function updateUserProfileInfo(user) {
    // Elementos del perfil
    const profileFullName = document.getElementById('profileFullName');
    const profileEmail = document.getElementById('profileEmail');
    const profilePhone = document.getElementById('profilePhone');
    const profileFarm = document.getElementById('profileFarm');
    const profileMemberSince = document.getElementById('profileMemberSince');
    const profileLastAccess = document.getElementById('profileLastAccess');
    const profileInitials = document.getElementById('profileInitials');
    const profileAvatarLetter = document.getElementById('profileAvatarLetter');

    console.log('Actualizando información de perfil:', user);

    // Actualizar nombre completo
    const fullName = user.nombre && user.apellido ?
        `${user.nombre} ${user.apellido}` :
        user.firstName && user.lastName ?
            `${user.firstName} ${user.lastName}` :
            'Usuario';

    if (profileFullName) {
        profileFullName.textContent = fullName;
    }

    // Actualizar iniciales en el avatar
    const initials = getInitials(fullName);
    if (profileInitials) {
        profileInitials.textContent = initials;
    }
    if (profileAvatarLetter) {
        profileAvatarLetter.textContent = initials;
    }

    // Actualizar email
    if (profileEmail) {
        profileEmail.textContent = user.email || 'No especificado';
    }

    // Actualizar teléfono
    if (profilePhone) {
        profilePhone.textContent = user.telefono || user.phone || 'No especificado';
    }

    // Actualizar nombre de granja
    if (profileFarm) {
        profileFarm.textContent = user.nombre_granja || user.farmName || 'No especificado';
    }

    // Actualizar fecha de registro
    if (profileMemberSince) {
        let memberSince = 'No disponible';
        if (user.fecha_registro) {
            memberSince = formatDate(user.fecha_registro);
        } else if (user.createdAt) {
            memberSince = formatDate(user.createdAt);
        }
        profileMemberSince.textContent = memberSince;
    }

    // Actualizar último acceso
    if (profileLastAccess) {
        let lastAccess = 'Ahora';
        if (user.fecha_ultimo_acceso) {
            lastAccess = formatDate(user.fecha_ultimo_acceso);
        } else if (user.lastAccess) {
            lastAccess = formatDate(user.lastAccess);
        }
        profileLastAccess.textContent = lastAccess;
    }
}

// Obtener iniciales del nombre
function getInitials(fullName) {
    if (!fullName || fullName === 'Usuario') {
        return 'U';
    }

    const names = fullName.trim().split(' ');
    if (names.length === 1) {
        return names[0].charAt(0).toUpperCase();
    }

    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
}

// Formatear fecha para mostrar
function formatDate(dateString) {
    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 0) {
            return 'Hoy';
        } else if (diffDays === 1) {
            return 'Ayer';
        } else if (diffDays < 7) {
            return `Hace ${diffDays} días`;
        } else {
            return date.toLocaleDateString('es-ES', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    } catch (error) {
        console.error('Error al formatear fecha:', error);
        return 'No disponible';
    }
}

// ========================================
// FUNCIONES DE ACCIONES
// ========================================

// Manejar logout desde la pestaña de perfil
async function handleProfileLogout() {
    if (confirm('¿Estás seguro de que quieres cerrar sesión?')) {
        console.log('Logout desde pestaña de perfil');
        
        // Cerrar pestaña primero
        closeProfileTab();
        
        // Ejecutar logout
        if (window.AuthMySQL) {
            await window.AuthMySQL.logout();
        } else if (window.AuthSystem) {
            window.AuthSystem.logout();
        }
        
        // Actualizar contenido después de logout
        setTimeout(() => {
            updateProfileTabContent();
        }, 500);
    }
}

// Ver perfil completo (placeholder)
function viewFullProfile() {
    console.log('Ver perfil completo');

    // Por ahora mostrar mensaje
    if (window.showNotification) {
        window.showNotification('Vista de perfil completo próximamente', 'info');
    } else {
        alert('Vista de perfil completo próximamente');
    }

    // Cerrar dropdown
    closeProfileTab();
}

// Editar perfil (placeholder)
function editProfile() {
    console.log('Función de editar perfil');

    // Por ahora mostrar mensaje
    if (window.showNotification) {
        window.showNotification('Función de edición de perfil próximamente', 'info');
    } else {
        alert('Función de edición de perfil próximamente');
    }

    // Cerrar dropdown
    closeProfileTab();
}

// ========================================
// EVENTOS Y LISTENERS
// ========================================

// Escuchar eventos de autenticación
function setupProfileTabListeners() {
    // Escuchar login
    window.addEventListener('authLogin', function(event) {
        console.log('Evento de login detectado en pestaña de perfil');
        setTimeout(() => {
            updateProfileTabContent();
        }, 100);
    });
    
    // Escuchar logout
    window.addEventListener('authLogout', function(event) {
        console.log('Evento de logout detectado en pestaña de perfil');
        setTimeout(() => {
            updateProfileTabContent();
        }, 100);
    });
    
    // Actualizar periódicamente
    setInterval(() => {
        if (profileTabOpen) {
            updateProfileTabContent();
        }
    }, 30000); // Cada 30 segundos
}

// ========================================
// INICIALIZACIÓN
// ========================================

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Esperar a que otros sistemas estén listos
    setTimeout(() => {
        initializeProfileTab();
        setupProfileTabListeners();
    }, 500);
});

// Exportar funciones para uso global
window.ProfileTab = {
    toggle: toggleProfileTab,
    open: openProfileTab,
    close: closeProfileTab,
    update: updateProfileTabContent,
    handleLogout: handleProfileLogout,
    editProfile: editProfile,
    viewFullProfile: viewFullProfile
};

// Hacer funciones disponibles globalmente
window.viewFullProfile = viewFullProfile;
window.editProfile = editProfile;
window.handleProfileLogout = handleProfileLogout;
