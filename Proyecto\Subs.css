* {
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', sans-serif;
    margin: 0;
    padding: 0;
  }

  body {
    background-color: #fff;
  }

  header {
    background-color: #41bd18;
    color: white;
    padding: 10px 10px;
    display: flex;
    align-items: center;
  }

  header img {
    height: 65px;
    margin-right: 10px;
  }

  header h1 {
    font-size: 24px;
    color: #000000;
  }

  .container {
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    padding: 40px 20px;
  }

  .left-section {
    max-width: 45%;
  }

  .left-section h2 {
    font-size: 22px;
    margin-bottom: 20px;
    color: #2c2c2c;
  }

  .left-section img {
    max-width: 100%;
    border-radius: 20px;
  }

  .right-section {
    max-width: 35%;
  }

  .right-section h2 {
    font-size: 28px;
    margin-bottom: 20px;
    color: #41bd18;
  }

  form {
    display: flex;
    flex-direction: column;
  }

  form label {
    margin: 8px 0 4px;
    font-size: 14px;
    color: #555;
  }

  form input[type="text"],
  form input[type="email"],
  form input[type="password"] {
    padding: 8px;
    border: 1px solid #41bd18;
    border-radius: 4px;
    outline: none;
  }

  form input:focus {
    border-color: #2d8d11;
    background-color: #e6ffe1;
  }

  .checkbox {
    margin: 15px 0;
    display: flex;
    align-items: center;
  }

  .checkbox input {
    margin-right: 8px;
  }

  .checkbox label {
    font-size: 14px;
  }

  .submit-btn {
    padding: 10px;
    background-color: #41bd18;
    color: white;
    border: none;
    font-size: 16px;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 10px;
  }

  .submit-btn:hover {
    background-color: #2d8d11;
  }

  footer {
    text-align: center;
    padding: 20px;
    font-size: 12px;
    color: #444;
  }