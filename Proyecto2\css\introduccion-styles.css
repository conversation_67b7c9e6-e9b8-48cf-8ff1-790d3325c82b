/* ========================================
   ESTILOS PARA LA PÁGINA DE INTRODUCCIÓN
   ======================================== */

/* Sección de introducción completa */
.intro-section-full {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e9 100%);
  padding: 40px;
  margin-bottom: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.intro-section-full::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #4caf50, #66bb6a, #81c784);
}

/* <PERSON><PERSON> de la sección */
.intro-header {
  text-align: center;
  margin-bottom: 40px;
}

.intro-icon {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30px;
  box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease;
}

.intro-icon:hover {
  transform: scale(1.1) rotate(5deg);
}

.intro-icon i {
  font-size: 4rem;
  color: white;
}

.intro-header h2 {
  color: #2e7d32;
  font-size: 2.5rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.divider-line {
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #4caf50, #66bb6a);
  margin: 0 auto 30px;
  border-radius: 2px;
}

/* Contenido principal */
.intro-content {
  max-width: 1200px;
  margin: 0 auto;
}

.intro-description {
  text-align: center;
  margin-bottom: 40px;
}

.highlight-text {
  font-size: 1.4rem;
  color: #2e7d32;
  font-weight: 600;
  margin-bottom: 20px;
  line-height: 1.6;
  background: linear-gradient(135deg, #f1f8e9, #e8f5e9);
  padding: 20px;
  border-radius: 10px;
  border-left: 5px solid #4caf50;
}

.intro-description p {
  font-size: 1.2rem;
  color: #555;
  line-height: 1.7;
  margin-bottom: 15px;
}

/* Imagen principal */
.intro-image-full {
  position: relative;
  margin: 40px 0;
  border-radius: 40px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  height: 650px;
}

.intro-image-full img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.intro-image-full:hover img {
  transform: scale(1.00);
}

.image-overlay-text {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: 30px;
  color: white;
}

.image-overlay-text h3 {
  font-size: 1.8rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.image-overlay-text p {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

/* Sección de equipo */
.team-section-full {
  margin: 50px 0;
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.team-section-full h3 {
  color: #2e7d32;
  font-size: 1.8rem;
  text-align: center;
  margin-bottom: 30px;
  font-weight: 600;
}

.expert-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.expert-card {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 4px solid #4caf50;
}

.expert-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: white;
}

.expert-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.expert-icon i {
  font-size: 2.5rem;
  color: #4caf50;
}

.expert-info {
  text-align: center;
}

.expert-info strong {
  display: block;
  color: #2e7d32;
  font-size: 1.3rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.expert-info p {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
}

/* Características principales */
.intro-features-full {
  margin: 50px 0;
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.intro-features-full h3 {
  color: #2e7d32;
  font-size: 1.8rem;
  text-align: center;
  margin-bottom: 30px;
  font-weight: 600;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
}

.feature-card {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: white;
}

.feature-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.feature-icon i {
  font-size: 2.5rem;
  color: #4caf50;
}

.feature-content strong {
  display: block;
  color: #2e7d32;
  font-size: 1.3rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.feature-content p {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
}

/* Etiquetas de sección */
.section-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
  margin-top: 40px;
}

.tag {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

.tag:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(76, 175, 80, 0.4);
}

/* Badge de sección */
.section-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.4rem;
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
  z-index: 10;
}

/* Responsive */
@media (max-width: 768px) {
  .intro-section-full {
    padding: 20px;
  }
  
  .intro-header h2 {
    font-size: 2rem;
  }
  
  .intro-icon {
    width: 100px;
    height: 100px;
  }
  
  .intro-icon i {
    font-size: 3rem;
  }
  
  .highlight-text {
    font-size: 1.2rem;
  }
  
  .intro-description p {
    font-size: 1.1rem;
  }
  
  .expert-grid,
  .features-grid {
    grid-template-columns: 1fr;
  }
}
