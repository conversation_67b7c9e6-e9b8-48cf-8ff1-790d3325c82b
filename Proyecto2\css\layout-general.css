/* ========================================
   ESTILOS GENERALES DEL LAYOUT
   ======================================== */

/* Reset y estilos base */
* {
  box-sizing: border-box;
  font-family: 'Roboto', sans-serif;
  margin: 0;
  padding: 0;
}

body {
  display: flex;
  min-height: 100vh;
  background: #fff;
}

/* Sidebar principal */
aside {
  position: fixed;
  top: 0;
  left: 0;
  width: 220px;
  height: 100%;
  background: linear-gradient(135deg, #4caf50, #2e7d32);
  color: rgb(255, 255, 255);
  padding-top: 15px;
  box-shadow: 3px 0 15px rgba(0,0,0,0.2);
  z-index: 100;
}

aside h1 {
  text-align: center;
  margin-bottom: 30px;
}

/* Logo */
.logo {
  text-align: center;
  padding: 10px 0 15px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  margin-bottom: 15px;
}

.logo img {
  max-width: 40%;
  height: auto;
  filter: drop-shadow(0 2px 5px rgba(0,0,0,0.2));
  transition: transform 0.3s ease;
}

.logo img:hover {
  transform: scale(1.05);
}

/* Menú lateral */
.menu {
  padding: 0 10px;
}

.menu a {
  display: flex;
  align-items: center;
  margin: 6px 0;
  padding: 10px 12px;
  background: rgba(255,255,255,0.15);
  text-decoration: none;
  color: #fff;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  font-size: 14px;
}

.menu a i {
  margin-right: 8px;
  width: 16px;
  text-align: center;
}

.menu a:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: rgba(255,255,255,0.1);
  transition: width 0.3s ease;
  z-index: -1;
}

.menu a:hover {
  background: rgba(255,255,255,0.2);
  transform: translateX(5px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.menu a:hover:before {
  width: 100%;
}

.menu a.active {
  background-color: rgba(255,255,255,0.3);
  color: white;
  font-weight: bold;
  box-shadow: 0 4px 10px rgba(0,0,0,0.15);
  border-left: 4px solid #fff;
}

/* Barra superior */
.top-bar {
  position: fixed;
  top: 0;
  left: 220px;
  right: 0;
  height: 60px;
  background-color: #aed7af;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 2px 2px 5px rgba(0,0,0,0.05);
  z-index: 90;
  border-left: none;
}

/* Contenedor de búsqueda */
.search-container {
  flex: 1;
  max-width: 500px;
  margin-right: 20px;
  position: relative;
}

.search-container input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border: 1px solid #ddd;
  border-radius: 50px;
  font-size: 14px;
  background-color: white;
  transition: all 0.3s ease;
}

.search-container input:focus {
  outline: none;
  border-color: #2e7d32;
  box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.1);
}

.search-container i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #2e7d32;
  font-size: 16px;
}

/* Acciones de la barra superior */
.top-bar-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.action-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2e7d32;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-button:hover {
  background-color: #e8f5e9;
  transform: translateY(-2px);
  box-shadow: 0 3px 5px rgba(0,0,0,0.1);
}

.action-button i {
  font-size: 18px;
}

/* Botón de notificación */
.notification-btn {
  position: relative;
}

/* Contenido principal */
main {
  margin-left: 220px;
  padding: 90px 30px 30px;
  font-family: 'Roboto', sans-serif;
  max-width: calc(100% - 220px);
  position: relative;
}

.header {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 20px;
  margin-top: 30px;
}

h2 {
  color: #319a11;
  margin-bottom: 20px;
}

/* Sección de imagen */
.image-section {
  margin: 20px 0;
  text-align: center;
}

.image-section img {
  max-width: 100%;
  border-radius: 16px;
  border: 4px solid #d0d0d0;
}

/* Tablas */
table {
  width: 100%;
  margin-top: 20px;
  border-collapse: collapse;
}

th, td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
}

th {
  background-color: #e6f4e6;
  color: #000;
}

/* Responsive */
@media (max-width: 768px) {
  aside {
    width: 200px;
  }

  .top-bar {
    left: 200px;
  }

  main {
    margin-left: 200px;
    max-width: calc(100% - 200px);
    padding: 80px 20px 20px;
  }

  .search-container {
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  aside {
    width: 180px;
  }

  .top-bar {
    left: 180px;
    padding: 0 15px;
  }

  main {
    margin-left: 180px;
    max-width: calc(100% - 180px);
    padding: 70px 15px 15px;
  }

  .menu a {
    font-size: 12px;
    padding: 8px 10px;
  }

  .search-container {
    max-width: 200px;
  }

  .action-button {
    width: 35px;
    height: 35px;
  }

  .action-button i {
    font-size: 16px;
  }
}
