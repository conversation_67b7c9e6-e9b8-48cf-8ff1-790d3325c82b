#!/bin/bash

echo "========================================"
echo "   SERVIDOR AVISOFT - INICIANDO"
echo "========================================"
echo

# Verificar si PHP está instalado
if ! command -v php &> /dev/null; then
    echo "❌ PHP no está instalado en tu sistema"
    echo
    echo "💡 Para instalar PHP:"
    echo "   • macOS: brew install php"
    echo "   • Ubuntu/Debian: sudo apt install php"
    echo "   • CentOS/RHEL: sudo yum install php"
    echo
    echo "📋 Después de instalar PHP, ejecuta este script nuevamente"
    exit 1
fi

echo "✅ PHP encontrado:"
php --version
echo

echo "🚀 Iniciando servidor PHP en puerto 8000..."
echo "🌐 Accede a: http://localhost:8000"
echo
echo "📋 URLs importantes:"
echo "   • Registro: http://localhost:8000/registro.html"
echo "   • Login: http://localhost:8000/login.html"
echo "   • Inicio: http://localhost:8000/index.html"
echo "   • Pruebas: http://localhost:8000/test-api.html"
echo
echo "⚠️  Para detener el servidor, presiona Ctrl+C"
echo

# Iniciar servidor PHP
php -S localhost:8000
