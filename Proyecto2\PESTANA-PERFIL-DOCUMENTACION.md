# 👤 Pestaña de Perfil AviSoft - Documentación

## 🎯 **Funcionalidad Implementada**

Se ha creado una pestaña de perfil flotante que se posiciona en el lado derecho de la pantalla, mostrando información diferente según el estado de autenticación del usuario.

## 📍 **Ubicación y Diseño**

### ✅ **Posición**
- **Ubicación**: Lado derecho de la pantalla, centrada verticalmente
- **Tipo**: Pestaña flotante fija (position: fixed)
- **Z-index**: 1000 (por encima de otros elementos)

### ✅ **Estados Visuales**
- **Cerrada**: Solo se ve el header vertical con icono y texto "Perfil"
- **Abierta**: Se despliega panel completo con información del usuario

## 🔄 **Comportamiento Dinámico**

### **Estado 1: Usuario NO Logueado**
```html
<!-- Contenido mostrado -->
<div class="profile-not-logged">
    <i class="fas fa-user-slash"></i>
    <h4>No has iniciado sesión</h4>
    <p>Inicia sesión para ver tu información de perfil</p>
    <button class="profile-btn login">Iniciar Sesión</button>
    <button class="profile-btn register">Registrarse</button>
</div>
```

### **Estado 2: Usuario Logueado**
```html
<!-- Contenido mostrado -->
<div class="profile-logged">
    <div class="profile-user-info">
        <div class="profile-avatar">👤</div>
        <div class="profile-details">
            <h3>Juan Pérez</h3>
            <p><EMAIL></p>
            <span class="profile-status">🟢 Activo</span>
        </div>
    </div>
    
    <div class="profile-info-grid">
        <div class="profile-info-item">
            <label>Teléfono:</label>
            <span>+57 ************</span>
        </div>
        <div class="profile-info-item">
            <label>Granja:</label>
            <span>Granja Los Pollos</span>
        </div>
        <div class="profile-info-item">
            <label>Miembro desde:</label>
            <span>15 Ene 2024</span>
        </div>
        <div class="profile-info-item">
            <label>Último acceso:</label>
            <span>Hoy</span>
        </div>
    </div>
    
    <div class="profile-actions">
        <button class="profile-btn edit">Editar Perfil</button>
        <button class="profile-btn logout">Cerrar Sesión</button>
    </div>
</div>
```

## 📁 **Archivos Creados**

### ✅ **CSS**
- **`css/profile-tab.css`** - Estilos completos para la pestaña de perfil

### ✅ **JavaScript**
- **`js/profile-tab.js`** - Funcionalidad completa de la pestaña

### ✅ **HTML**
- Agregado a **`index.html`**, **`introduccion.html`**, **`Informacion.html`**

## 🎨 **Características de Diseño**

### ✅ **Header de la Pestaña**
- **Color**: Verde #2e7d32 (color del software)
- **Icono**: Usuario (fas fa-user)
- **Texto**: "Perfil" en orientación vertical
- **Hover**: Se desplaza ligeramente hacia la izquierda
- **Flecha**: Indica estado abierto/cerrado

### ✅ **Panel de Contenido**
- **Ancho**: 320px (280px en móvil)
- **Fondo**: Blanco con sombra sutil
- **Animación**: Deslizamiento suave desde la derecha
- **Scroll**: Personalizado si el contenido es muy largo

### ✅ **Información del Usuario**
- **Avatar**: Círculo verde con icono de usuario
- **Nombre completo**: Título principal
- **Email**: Subtítulo
- **Estado**: Indicador "Activo" con punto verde

### ✅ **Grid de Información**
- **Teléfono**: Del registro del usuario
- **Granja**: Nombre de la granja del usuario
- **Miembro desde**: Fecha de registro formateada
- **Último acceso**: Fecha del último login

## ⚡ **Funcionalidades**

### ✅ **Interacciones**
- **Clic en header**: Abre/cierra la pestaña
- **Clic fuera**: Cierra la pestaña automáticamente
- **Tecla Escape**: Cierra la pestaña
- **Botones internos**: Acciones específicas

### ✅ **Botones de Acción**

#### **Usuario NO Logueado**
- **"Iniciar Sesión"**: Redirige a `login.html`
- **"Registrarse"**: Redirige a `registro.html`

#### **Usuario Logueado**
- **"Editar Perfil"**: Función placeholder (próximamente)
- **"Cerrar Sesión"**: Logout con confirmación

### ✅ **Actualización Automática**
- **Al hacer login**: Se actualiza automáticamente
- **Al hacer logout**: Se actualiza automáticamente
- **Al cargar página**: Se verifica estado y actualiza
- **Cada 30 segundos**: Actualización periódica si está abierta

## 🔧 **Integración con Sistema de Autenticación**

### ✅ **Compatibilidad**
- **Sistema MySQL**: Prioridad principal
- **Sistema original**: Fallback automático
- **Eventos personalizados**: Responde a `authLogin` y `authLogout`

### ✅ **Datos Mostrados**
```javascript
// Datos del sistema MySQL
{
    nombre: "Juan",
    apellido: "Pérez", 
    email: "<EMAIL>",
    telefono: "+57 ************",
    nombre_granja: "Granja Los Pollos",
    fecha_registro: "2024-01-15T10:30:00Z",
    fecha_ultimo_acceso: "2024-01-20T15:45:00Z"
}
```

## 📱 **Responsive Design**

### ✅ **Desktop (>768px)**
- **Ancho**: 320px
- **Header**: 50px de ancho
- **Padding**: 20px interno

### ✅ **Tablet (768px)**
- **Ancho**: 280px
- **Header**: 45px de ancho
- **Padding**: 16px interno

### ✅ **Mobile (<480px)**
- **Ancho**: 260px
- **Header**: 40px de ancho
- **Padding**: 14px interno
- **Avatar**: Más pequeño (45px)

## 🎯 **Funciones JavaScript Principales**

### ✅ **Inicialización**
```javascript
initializeProfileTab()          // Configurar eventos y elementos
setupProfileTabListeners()      // Escuchar eventos de auth
```

### ✅ **Control de Estado**
```javascript
toggleProfileTab()              // Abrir/cerrar pestaña
openProfileTab()               // Abrir pestaña
closeProfileTab()              // Cerrar pestaña
```

### ✅ **Actualización de Contenido**
```javascript
updateProfileTabContent()       // Actualizar según estado de auth
updateUserProfileInfo(user)     // Actualizar datos del usuario
formatDate(dateString)          // Formatear fechas amigables
```

### ✅ **Acciones**
```javascript
handleProfileLogout()          // Logout desde la pestaña
editProfile()                  // Editar perfil (placeholder)
```

## 🎨 **Animaciones y Transiciones**

### ✅ **Apertura/Cierre**
- **Duración**: 0.3 segundos
- **Easing**: ease
- **Efecto**: Deslizamiento horizontal + fade

### ✅ **Hover Effects**
- **Header**: Desplazamiento hacia la izquierda
- **Botones**: Cambio de color suave
- **Flecha**: Rotación 180°

### ✅ **Estados**
- **Abierto**: `transform: translateX(0)`, `opacity: 1`
- **Cerrado**: `transform: translateX(100%)`, `opacity: 0`

## 🧪 **Casos de Uso**

### ✅ **Caso 1: Usuario Nuevo**
1. Usuario llega al sitio sin estar logueado
2. Ve la pestaña "Perfil" en el lado derecho
3. Hace clic y ve mensaje "No has iniciado sesión"
4. Puede hacer clic en "Iniciar Sesión" o "Registrarse"

### ✅ **Caso 2: Usuario Registrado**
1. Usuario se registra exitosamente
2. Es redirigido a login
3. Inicia sesión
4. La pestaña se actualiza automáticamente con su información

### ✅ **Caso 3: Usuario Navegando**
1. Usuario logueado navega entre páginas
2. La pestaña mantiene su información en todas las páginas
3. Puede abrir/cerrar la pestaña en cualquier momento

### ✅ **Caso 4: Logout**
1. Usuario hace clic en "Cerrar Sesión" desde la pestaña
2. Confirma la acción
3. La pestaña se cierra automáticamente
4. Al abrirla nuevamente, muestra estado no logueado

## 🎉 **Beneficios Implementados**

### ✅ **Experiencia de Usuario**
- **Acceso rápido**: Información del perfil siempre disponible
- **No intrusivo**: Solo se muestra cuando se necesita
- **Consistente**: Mismo comportamiento en todas las páginas
- **Informativo**: Muestra datos relevantes del usuario

### ✅ **Funcionalidad**
- **Actualización automática**: Responde a cambios de estado
- **Integración completa**: Funciona con sistema de autenticación
- **Responsive**: Se adapta a todos los dispositivos
- **Accesible**: Navegable con teclado (Escape para cerrar)

### ✅ **Desarrollo**
- **Modular**: Código separado y reutilizable
- **Extensible**: Fácil agregar nuevas funcionalidades
- **Mantenible**: Estructura clara y documentada
- **Compatible**: Funciona con ambos sistemas de auth

## 🎯 **Estado Final**

✅ **Pestaña de perfil** implementada en todas las páginas  
✅ **Estado dinámico** según autenticación del usuario  
✅ **Información completa** de la base de datos mostrada  
✅ **Diseño responsive** para todos los dispositivos  
✅ **Animaciones suaves** y transiciones profesionales  
✅ **Integración completa** con sistema de autenticación MySQL  
✅ **Funcionalidades de acción** (logout, editar perfil)  

¡La pestaña de perfil está completamente implementada y funcionando! 🎉👤✨
