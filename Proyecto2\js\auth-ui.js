// ========================================
// INTERFAZ DE AUTENTICACIÓN AVISOFT
// ========================================

// Variables globales para la UI
let userDropdownOpen = false;

// ========================================
// FUNCIONES DE INTERFAZ
// ========================================

// Actualizar interfaz según estado de autenticación
function updateAuthUI() {
    const authButtons = document.getElementById('authButtons');
    const userMenu = document.getElementById('userMenu');

    if (!authButtons || !userMenu) return;

    // Verificar si hay sistema MySQL disponible
    if (window.AuthMySQL && window.AuthMySQL.isLoggedIn()) {
        const currentUser = window.AuthMySQL.getCurrentUser();

        console.log('AuthUI: Usuario logueado detectado', currentUser);

        // Ocultar botones de login/registro
        authButtons.style.display = 'none';
        authButtons.style.visibility = 'hidden';

        // Mostrar menú de usuario
        userMenu.style.display = 'flex';
        userMenu.style.visibility = 'visible';

        // Actualizar información del usuario
        updateUserInfoMySQL(currentUser);

    } else if (window.AuthSystem && window.AuthSystem.isAuthenticated()) {
        // Fallback al sistema original
        const currentUser = window.AuthSystem.getCurrentUser();

        // Ocultar botones de login/registro
        authButtons.style.display = 'none';
        authButtons.style.visibility = 'hidden';

        // Mostrar menú de usuario
        userMenu.style.display = 'flex';
        userMenu.style.visibility = 'visible';

        // Actualizar información del usuario
        updateUserInfo(currentUser.user);

    } else {
        console.log('AuthUI: Usuario no logueado');

        // Mostrar botones de login/registro
        authButtons.style.display = 'flex';
        authButtons.style.visibility = 'visible';

        // Ocultar menú de usuario
        userMenu.style.display = 'none';
        userMenu.style.visibility = 'hidden';
    }
}

// Actualizar información del usuario en la interfaz (sistema original)
function updateUserInfo(user) {
    const userName = document.getElementById('userName');
    const userFullName = document.getElementById('userFullName');
    const userEmail = document.getElementById('userEmail');

    if (userName) {
        userName.textContent = user.firstName;
    }

    if (userFullName) {
        userFullName.textContent = `${user.firstName} ${user.lastName}`;
    }

    if (userEmail) {
        userEmail.textContent = user.email;
    }
}

// Actualizar información del usuario en la interfaz (sistema MySQL)
function updateUserInfoMySQL(user) {
    const userName = document.getElementById('userName');
    const userFullName = document.getElementById('userFullName');
    const userEmail = document.getElementById('userEmail');

    console.log('Actualizando información de usuario MySQL:', user);

    if (userName) {
        userName.textContent = user.nombre;
        console.log('Nombre actualizado:', user.nombre);
    }

    if (userFullName) {
        userFullName.textContent = `${user.nombre} ${user.apellido}`;
        console.log('Nombre completo actualizado:', `${user.nombre} ${user.apellido}`);
    }

    if (userEmail) {
        userEmail.textContent = user.email;
        console.log('Email actualizado:', user.email);
    }
}

// Toggle del dropdown de usuario
function toggleUserDropdown() {
    const userDropdown = document.getElementById('userDropdown');
    
    if (!userDropdown) return;
    
    userDropdownOpen = !userDropdownOpen;
    
    if (userDropdownOpen) {
        userDropdown.classList.add('show');
        // Cerrar al hacer clic fuera
        setTimeout(() => {
            document.addEventListener('click', closeUserDropdownOnClickOutside);
        }, 100);
    } else {
        userDropdown.classList.remove('show');
        document.removeEventListener('click', closeUserDropdownOnClickOutside);
    }
}

// Cerrar dropdown al hacer clic fuera
function closeUserDropdownOnClickOutside(event) {
    const userMenu = document.getElementById('userMenu');
    const userDropdown = document.getElementById('userDropdown');
    
    if (!userMenu || !userDropdown) return;
    
    if (!userMenu.contains(event.target)) {
        userDropdown.classList.remove('show');
        userDropdownOpen = false;
        document.removeEventListener('click', closeUserDropdownOnClickOutside);
    }
}

// Manejar logout
async function handleLogout() {
    // Mostrar confirmación
    if (confirm('¿Estás seguro de que quieres cerrar sesión?')) {
        console.log('Usuario confirmó logout');

        // Intentar logout con MySQL primero
        if (window.AuthMySQL) {
            console.log('Ejecutando logout MySQL');
            await window.AuthMySQL.logout();

            // Forzar actualización de la interfaz
            setTimeout(() => {
                updateAuthUI();
                console.log('Interfaz actualizada después de logout MySQL');
            }, 100);

        } else if (window.AuthSystem) {
            // Fallback al sistema original
            console.log('Ejecutando logout sistema original');
            window.AuthSystem.logout();

            // Forzar actualización de la interfaz
            setTimeout(() => {
                updateAuthUI();
            }, 100);
        }
    }
}

// Mostrar notificación de bienvenida (sistema original)
function showWelcomeMessage(user) {
    if (window.AuthSystem) {
        const message = `¡Bienvenido de vuelta, ${user.firstName}!`;
        window.AuthSystem.showNotification(message, 'success');
    }
}

// Mostrar notificación de bienvenida (sistema MySQL)
function showWelcomeMessageMySQL(user) {
    const message = `¡Bienvenido de vuelta, ${user.nombre}!`;
    if (window.showNotification) {
        window.showNotification(message, 'success');
    } else {
        console.log('Mensaje de bienvenida:', message);
    }
}

// ========================================
// INICIALIZACIÓN Y EVENTOS
// ========================================

document.addEventListener('DOMContentLoaded', function() {
    // Esperar a que AuthMySQL o AuthSystem esté disponible
    const checkAuthSystem = setInterval(() => {
        if (window.AuthMySQL || window.AuthSystem) {
            clearInterval(checkAuthSystem);
            console.log('Sistema de autenticación detectado, inicializando UI');
            initializeAuthUI();
        }
    }, 100);
});

function initializeAuthUI() {
    console.log('Inicializando interfaz de autenticación');

    // Actualizar interfaz inicial
    updateAuthUI();

    // Configurar eventos del botón de usuario
    const userBtn = document.getElementById('userBtn');
    if (userBtn) {
        userBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleUserDropdown();
        });
        console.log('Evento de botón de usuario configurado');
    }

    // Configurar evento de logout
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleLogout();
        });
        console.log('Evento de logout configurado');
    }

    // Mostrar mensaje de bienvenida si el usuario acaba de iniciar sesión
    if (window.AuthMySQL && window.AuthMySQL.isLoggedIn()) {
        const currentUser = window.AuthMySQL.getCurrentUser();
        console.log('Usuario MySQL logueado, mostrando bienvenida');
        setTimeout(() => {
            showWelcomeMessageMySQL(currentUser);
        }, 1000);
    } else if (window.AuthSystem && window.AuthSystem.isAuthenticated()) {
        const currentUser = window.AuthSystem.getCurrentUser();
        const loginTime = new Date(currentUser.loginTime);
        const now = new Date();
        const timeDiff = now - loginTime;

        // Si el login fue hace menos de 5 minutos, mostrar bienvenida
        if (timeDiff < 5 * 60 * 1000) {
            setTimeout(() => {
                showWelcomeMessage(currentUser.user);
            }, 1000);
        }
    }

    // Configurar eventos para cerrar dropdowns con Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            if (userDropdownOpen) {
                const userDropdown = document.getElementById('userDropdown');
                if (userDropdown) {
                    userDropdown.classList.remove('show');
                    userDropdownOpen = false;
                }
            }
        }
    });

    // Verificar periódicamente el estado de autenticación
    setInterval(() => {
        updateAuthUI();
    }, 30000); // Cada 30 segundos

    // Escuchar eventos de autenticación
    window.addEventListener('authLogin', function(event) {
        console.log('Evento de login detectado:', event.detail);
        setTimeout(() => {
            updateAuthUI();
        }, 100);
    });

    window.addEventListener('authLogout', function(event) {
        console.log('Evento de logout detectado:', event.detail);
        setTimeout(() => {
            updateAuthUI();
        }, 100);
    });

    console.log('Interfaz de autenticación inicializada');
}

// ========================================
// FUNCIONES AUXILIARES PARA OTRAS PÁGINAS
// ========================================

// Función para agregar botones de auth a otras páginas
function addAuthButtonsToPage() {
    const actionButtons = document.querySelector('.action-buttons');
    
    if (!actionButtons) return;
    
    // Verificar si ya existen los botones
    if (document.getElementById('authButtons')) return;
    
    // Crear botones de autenticación
    const authButtonsHTML = `
        <div class="auth-buttons" id="authButtons">
            <div class="action-button login-btn" onclick="window.location.href='login.html'">
                <i class="fas fa-sign-in-alt"></i>
                <span>Iniciar Sesión</span>
            </div>
            <div class="action-button register-btn" onclick="window.location.href='registro.html'">
                <i class="fas fa-user-plus"></i>
                <span>Registrarse</span>
            </div>
        </div>

        <div class="user-menu" id="userMenu" style="display: none;">
            <div class="action-button user-btn" id="userBtn">
                <i class="fas fa-user-circle"></i>
                <span id="userName">Usuario</span>
            </div>
            
            <div class="user-dropdown" id="userDropdown">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-details">
                        <h4 id="userFullName">Nombre Usuario</h4>
                        <p id="userEmail"><EMAIL></p>
                    </div>
                </div>
                <div class="user-menu-items">
                    <a href="#" class="user-menu-item">
                        <i class="fas fa-user-edit"></i>
                        <span>Editar Perfil</span>
                    </a>
                    <a href="#" class="user-menu-item">
                        <i class="fas fa-cog"></i>
                        <span>Configuración</span>
                    </a>
                    <a href="#" class="user-menu-item">
                        <i class="fas fa-history"></i>
                        <span>Historial</span>
                    </a>
                    <div class="menu-divider"></div>
                    <a href="#" class="user-menu-item logout-item" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Cerrar Sesión</span>
                    </a>
                </div>
            </div>
        </div>
    `;
    
    // Insertar después del botón de notificaciones
    const notificationBtn = document.querySelector('.notification-btn');
    if (notificationBtn) {
        notificationBtn.insertAdjacentHTML('afterend', authButtonsHTML);
    } else {
        actionButtons.insertAdjacentHTML('beforeend', authButtonsHTML);
    }
    
    // Reinicializar eventos
    setTimeout(() => {
        initializeAuthUI();
    }, 100);
}

// Función para verificar si el usuario debe estar autenticado
function requireAuthentication() {
    if (!window.AuthSystem || !window.AuthSystem.isAuthenticated()) {
        window.AuthSystem.showNotification('Debes iniciar sesión para acceder a esta función', 'warning');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
        return false;
    }
    return true;
}

// Exportar funciones para uso global
window.AuthUI = {
    updateAuthUI,
    addAuthButtonsToPage,
    requireAuthentication,
    showWelcomeMessage
};
