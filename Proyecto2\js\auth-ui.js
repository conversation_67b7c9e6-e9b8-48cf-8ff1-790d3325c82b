// ========================================
// INTERFAZ DE AUTENTICACIÓN AVISOFT
// ========================================

// Variables globales para la UI
let userDropdownOpen = false;

// ========================================
// FUNCIONES DE INTERFAZ
// ========================================

// Actualizar interfaz según estado de autenticación
function updateAuthUI() {
    const authButtons = document.getElementById('authButtons');
    const userMenu = document.getElementById('userMenu');
    
    if (!authButtons || !userMenu) return;
    
    if (window.AuthSystem && window.AuthSystem.isAuthenticated()) {
        const currentUser = window.AuthSystem.getCurrentUser();
        
        // Ocultar botones de login/registro
        authButtons.style.display = 'none';
        
        // Mostrar menú de usuario
        userMenu.style.display = 'block';
        
        // Actualizar información del usuario
        updateUserInfo(currentUser.user);
        
    } else {
        // Mostrar botones de login/registro
        authButtons.style.display = 'flex';
        
        // Ocultar menú de usuario
        userMenu.style.display = 'none';
    }
}

// Actualizar información del usuario en la interfaz
function updateUserInfo(user) {
    const userName = document.getElementById('userName');
    const userFullName = document.getElementById('userFullName');
    const userEmail = document.getElementById('userEmail');
    
    if (userName) {
        userName.textContent = user.firstName;
    }
    
    if (userFullName) {
        userFullName.textContent = `${user.firstName} ${user.lastName}`;
    }
    
    if (userEmail) {
        userEmail.textContent = user.email;
    }
}

// Toggle del dropdown de usuario
function toggleUserDropdown() {
    const userDropdown = document.getElementById('userDropdown');
    
    if (!userDropdown) return;
    
    userDropdownOpen = !userDropdownOpen;
    
    if (userDropdownOpen) {
        userDropdown.classList.add('show');
        // Cerrar al hacer clic fuera
        setTimeout(() => {
            document.addEventListener('click', closeUserDropdownOnClickOutside);
        }, 100);
    } else {
        userDropdown.classList.remove('show');
        document.removeEventListener('click', closeUserDropdownOnClickOutside);
    }
}

// Cerrar dropdown al hacer clic fuera
function closeUserDropdownOnClickOutside(event) {
    const userMenu = document.getElementById('userMenu');
    const userDropdown = document.getElementById('userDropdown');
    
    if (!userMenu || !userDropdown) return;
    
    if (!userMenu.contains(event.target)) {
        userDropdown.classList.remove('show');
        userDropdownOpen = false;
        document.removeEventListener('click', closeUserDropdownOnClickOutside);
    }
}

// Manejar logout
function handleLogout() {
    if (window.AuthSystem) {
        // Mostrar confirmación
        if (confirm('¿Estás seguro de que quieres cerrar sesión?')) {
            window.AuthSystem.logout();
        }
    }
}

// Mostrar notificación de bienvenida
function showWelcomeMessage(user) {
    if (window.AuthSystem) {
        const message = `¡Bienvenido de vuelta, ${user.firstName}!`;
        window.AuthSystem.showNotification(message, 'success');
    }
}

// ========================================
// INICIALIZACIÓN Y EVENTOS
// ========================================

document.addEventListener('DOMContentLoaded', function() {
    // Esperar a que AuthSystem esté disponible
    const checkAuthSystem = setInterval(() => {
        if (window.AuthSystem) {
            clearInterval(checkAuthSystem);
            initializeAuthUI();
        }
    }, 100);
});

function initializeAuthUI() {
    // Actualizar interfaz inicial
    updateAuthUI();
    
    // Configurar eventos del botón de usuario
    const userBtn = document.getElementById('userBtn');
    if (userBtn) {
        userBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleUserDropdown();
        });
    }
    
    // Configurar evento de logout
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleLogout();
        });
    }
    
    // Mostrar mensaje de bienvenida si el usuario acaba de iniciar sesión
    if (window.AuthSystem.isAuthenticated()) {
        const currentUser = window.AuthSystem.getCurrentUser();
        const loginTime = new Date(currentUser.loginTime);
        const now = new Date();
        const timeDiff = now - loginTime;
        
        // Si el login fue hace menos de 5 minutos, mostrar bienvenida
        if (timeDiff < 5 * 60 * 1000) {
            setTimeout(() => {
                showWelcomeMessage(currentUser.user);
            }, 1000);
        }
    }
    
    // Configurar eventos para cerrar dropdowns con Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            if (userDropdownOpen) {
                const userDropdown = document.getElementById('userDropdown');
                if (userDropdown) {
                    userDropdown.classList.remove('show');
                    userDropdownOpen = false;
                }
            }
        }
    });
    
    // Verificar periódicamente el estado de autenticación
    setInterval(() => {
        updateAuthUI();
    }, 30000); // Cada 30 segundos
}

// ========================================
// FUNCIONES AUXILIARES PARA OTRAS PÁGINAS
// ========================================

// Función para agregar botones de auth a otras páginas
function addAuthButtonsToPage() {
    const actionButtons = document.querySelector('.action-buttons');
    
    if (!actionButtons) return;
    
    // Verificar si ya existen los botones
    if (document.getElementById('authButtons')) return;
    
    // Crear botones de autenticación
    const authButtonsHTML = `
        <div class="auth-buttons" id="authButtons">
            <div class="action-button login-btn" onclick="window.location.href='login.html'">
                <i class="fas fa-sign-in-alt"></i>
                <span>Iniciar Sesión</span>
            </div>
            <div class="action-button register-btn" onclick="window.location.href='registro.html'">
                <i class="fas fa-user-plus"></i>
                <span>Registrarse</span>
            </div>
        </div>

        <div class="user-menu" id="userMenu" style="display: none;">
            <div class="action-button user-btn" id="userBtn">
                <i class="fas fa-user-circle"></i>
                <span id="userName">Usuario</span>
            </div>
            
            <div class="user-dropdown" id="userDropdown">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-details">
                        <h4 id="userFullName">Nombre Usuario</h4>
                        <p id="userEmail"><EMAIL></p>
                    </div>
                </div>
                <div class="user-menu-items">
                    <a href="#" class="user-menu-item">
                        <i class="fas fa-user-edit"></i>
                        <span>Editar Perfil</span>
                    </a>
                    <a href="#" class="user-menu-item">
                        <i class="fas fa-cog"></i>
                        <span>Configuración</span>
                    </a>
                    <a href="#" class="user-menu-item">
                        <i class="fas fa-history"></i>
                        <span>Historial</span>
                    </a>
                    <div class="menu-divider"></div>
                    <a href="#" class="user-menu-item logout-item" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Cerrar Sesión</span>
                    </a>
                </div>
            </div>
        </div>
    `;
    
    // Insertar después del botón de notificaciones
    const notificationBtn = document.querySelector('.notification-btn');
    if (notificationBtn) {
        notificationBtn.insertAdjacentHTML('afterend', authButtonsHTML);
    } else {
        actionButtons.insertAdjacentHTML('beforeend', authButtonsHTML);
    }
    
    // Reinicializar eventos
    setTimeout(() => {
        initializeAuthUI();
    }, 100);
}

// Función para verificar si el usuario debe estar autenticado
function requireAuthentication() {
    if (!window.AuthSystem || !window.AuthSystem.isAuthenticated()) {
        window.AuthSystem.showNotification('Debes iniciar sesión para acceder a esta función', 'warning');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
        return false;
    }
    return true;
}

// Exportar funciones para uso global
window.AuthUI = {
    updateAuthUI,
    addAuthButtonsToPage,
    requireAuthentication,
    showWelcomeMessage
};
