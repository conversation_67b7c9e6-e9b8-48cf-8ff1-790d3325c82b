# ========================================
# CONFIGURACIÓN APACHE PARA AVISOFT
# ========================================

# Habilitar reescritura de URLs
RewriteEngine On

# Configuración de CORS para APIs
<IfModule mod_headers.c>
    # Permitir CORS para APIs
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    Header always set Access-Control-Max-Age "3600"
    
    # Manejar preflight requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>

# Configuración de seguridad
<IfModule mod_headers.c>
    # Prevenir clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevenir MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Habilitar XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Configurar Content Security Policy
    Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data:; img-src 'self' https: data:; font-src 'self' https: data:;"
</IfModule>

# Configuración de cache
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Cache para archivos estáticos
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
    
    # No cache para APIs
    ExpiresByType application/json "access plus 0 seconds"
    ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# Configuración de compresión
<IfModule mod_deflate.c>
    # Comprimir archivos de texto
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Redirecciones y rutas amigables
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Redirigir a index.html si no se encuentra el archivo
RewriteCond %{REQUEST_URI} !^/api/
RewriteCond %{REQUEST_URI} !\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$
RewriteRule ^(.*)$ index.html [L]

# Configuración específica para APIs PHP
<Files "*.php">
    # Configurar tipo de contenido para APIs
    Header set Content-Type "application/json; charset=utf-8"
    
    # Configurar cache para APIs
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</Files>

# Proteger archivos sensibles
<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# Proteger directorio de configuración
<Directory "config">
    Order allow,deny
    Deny from all
</Directory>

# Proteger directorio de logs
<Directory "logs">
    Order allow,deny
    Deny from all
</Directory>

# Configuración de errores personalizados
ErrorDocument 404 /index.html
ErrorDocument 403 /index.html
ErrorDocument 500 /index.html

# Configuración de PHP (si está disponible)
<IfModule mod_php.c>
    # Configurar zona horaria
    php_value date.timezone "America/Bogota"
    
    # Configurar límites
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    php_value post_max_size 10M
    php_value upload_max_filesize 5M
    
    # Configurar sesiones
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_value session.use_strict_mode 1
    
    # Configurar errores
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log "logs/php_errors.log"
</IfModule>

# Configuración de tipos MIME adicionales
AddType application/json .json
AddType application/javascript .js
AddType text/css .css
AddType image/svg+xml .svg

# Prevenir acceso directo a archivos de configuración
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Configuración de índices de directorio
Options -Indexes

# Configuración de seguimiento de servidor
ServerSignature Off
