/* ========================================
   ESTILOS PARA LA PÁGINA DE INICIO
   ======================================== */

/* Sección hero principal */
.hero-section {
  display: flex;
  align-items: center;
  gap: 40px;
  padding: 40px 0;
  margin-bottom: 60px;
  margin-top: 20px;
}

.hero-content {
  flex: 1;
}

.hero-title {
  font-size: 2.5rem;
  color: #2e7d32;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #555;
  margin-bottom: 30px;
  line-height: 1.5;
}

.hero-buttons {
  display: flex;
  gap: 15px;
}

.primary-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primary-button:hover {
  background-color: #388e3c;
}

.secondary-button {
  background-color: white;
  color: #4CAF50;
  border: 2px solid #4CAF50;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondary-button:hover {
  background-color: #f1f8e9;
}

.hero-image {
  flex: 1;
  position: relative;
}

.hero-image img {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Títulos y descripciones de sección */
.section-title {
  color: #2e7d32;
  font-size: 1.8rem;
  margin-bottom: 15px;
  position: relative;
}

.section-description {
  color: #555;
  font-size: 1.1rem;
  margin-bottom: 30px;
  max-width: 800px;
  line-height: 1.5;
}

/* Sección de características */
.features-section {
  padding: 60px 0;
  background-color: #f9f9f9;
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 2.5rem;
  color: #4CAF50;
  margin-bottom: 20px;
}

.feature-card h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* Sección de beneficios */
.benefits-section {
  display: flex;
  align-items: center;
  gap: 40px;
  padding: 60px 0;
  margin-bottom: 60px;
}

.benefits-content {
  flex: 1;
}

.benefits-list {
  list-style: none;
  padding: 0;
}

.benefits-list li {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  line-height: 1.5;
}

.check-icon {
  color: #4CAF50;
  font-size: 1.2rem;
  margin-right: 10px;
  font-weight: bold;
}

.benefits-image {
  flex: 1;
}

.benefits-image img {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Sección de testimonios */
.testimonials-section {
  padding: 60px 0;
  background-color: #f9f9f9;
  margin-bottom: 60px;
}

.testimonials-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.testimonial-card {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.testimonial-content {
  margin-bottom: 20px;
}

.testimonial-content p {
  font-style: italic;
  color: #555;
  line-height: 1.6;
  position: relative;
  padding: 0 20px;
}

.testimonial-content p:before,
.testimonial-content p:after {
  content: '"';
  font-size: 1.5rem;
  color: #4CAF50;
  font-weight: bold;
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.author-info h4 {
  margin: 0;
  color: #333;
}

.author-info p {
  margin: 5px 0 0;
  color: #777;
  font-size: 0.9rem;
}

/* Sección CTA */
.cta-section {
  text-align: center;
  padding: 60px 0;
  background-color: #e8f5e9;
  margin-bottom: 60px;
  border-radius: 10px;
}

.cta-section h2 {
  color: #2e7d32;
  margin-bottom: 15px;
  font-size: 2rem;
}

.cta-section p {
  color: #555;
  margin-bottom: 30px;
  font-size: 1.1rem;
}

/* CTA general */
.cta {
  text-align: center;
  margin: 30px 0;
}

.cta button {
  background: #41bd18;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.cta button:hover {
  background: #319a11;
}

/* Sección de información básica */
.info-section {
  display: flex;
  gap: 20px;
}

.info-box, .benefits-box {
  flex: 1;
  padding: 20px;
  border: 2px solid #62b420;
  border-radius: 12px;
}

.info-box h2, .benefits-box h2 {
  color: #2b9a06;
  font-size: 18px;
  margin-bottom: 10px;
}

.info-box p, .benefits-box ul {
  font-size: 16px;
  font-weight: bold;
}

.benefits-box ul {
  list-style-type: '✅';
  padding-left: 20px;
}

/* Responsive para inicio */
@media (max-width: 768px) {
  .hero-section {
    flex-direction: column;
    text-align: center;
    gap: 30px;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .hero-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .benefits-section {
    flex-direction: column;
    text-align: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .testimonials-container {
    grid-template-columns: 1fr;
  }
  
  .info-section {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.8rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .section-description {
    font-size: 1rem;
  }
  
  .features-section,
  .testimonials-section {
    padding: 40px 0;
  }
  
  .feature-card,
  .testimonial-card {
    padding: 20px;
  }
  
  .cta-section {
    padding: 40px 20px;
  }
}
