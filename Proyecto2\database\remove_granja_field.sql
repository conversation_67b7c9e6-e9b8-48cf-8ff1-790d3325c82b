-- ========================================
-- SCRIPT PARA ELIMINAR CAMPO NOMBRE_GRANJA
-- Base de datos: Avisoftdatabase
-- ========================================

USE Avisoftdatabase;

-- Verificar si la columna existe antes de eliminarla
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'Avisoftdatabase' 
AND TABLE_NAME = 'usuarios' 
AND COLUMN_NAME = 'nombre_granja';

-- Eliminar la columna nombre_granja de la tabla usuarios
ALTER TABLE usuarios DROP COLUMN IF EXISTS nombre_granja;

-- Recrear la vista sin el campo nombre_granja
DROP VIEW IF EXISTS vista_usuarios_activos;

CREATE OR REPLACE VIEW vista_usuarios_activos AS
SELECT 
    id,
    nombre,
    apellido,
    email,
    telefono,
    fecha_registro,
    fecha_ultimo_acceso,
    suscrito_newsletter
FROM usuarios 
WHERE activo = TRUE;

-- Verificar que la columna fue eliminada
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'Avisoftdatabase' 
AND TABLE_NAME = 'usuarios' 
ORDER BY ORDINAL_POSITION;

-- Mostrar estructura actualizada de la tabla
DESCRIBE usuarios;

SELECT 'Campo nombre_granja eliminado exitosamente de la tabla usuarios' as Resultado;
