<?php
// ========================================
// API DE VERIFICACIÓN DE SESIÓN AVISOFT
// ========================================

// Headers para CORS y JSON
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Incluir dependencias
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/User.php';

try {
    // Obtener token de sesión
    $token = null;
    
    // Verificar en headers Authorization
    $headers = getallheaders();
    if (isset($headers['Authorization'])) {
        $auth_header = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $token = $matches[1];
        }
    }
    
    // Verificar en POST data
    if (!$token && $_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        if (isset($data['token'])) {
            $token = $data['token'];
        }
    }
    
    // Verificar en cookies
    if (!$token && isset($_COOKIE['avisoft_session'])) {
        $token = $_COOKIE['avisoft_session'];
    }
    
    // Verificar en parámetros GET
    if (!$token && isset($_GET['token'])) {
        $token = $_GET['token'];
    }

    if (!$token) {
        jsonResponse(['success' => false, 'message' => 'Token de sesión no proporcionado'], 401);
    }

    // Crear instancia de User
    $user = new User();

    // Verificar sesión
    $session = $user->verifySession($token);

    if ($session) {
        // Sesión válida
        $response = [
            'success' => true,
            'message' => 'Sesión válida',
            'user' => [
                'id' => $session['user_id'],
                'nombre' => $session['nombre'],
                'apellido' => $session['apellido'],
                'email' => $session['email']
            ],
            'session' => [
                'token' => $session['token_sesion'],
                'fecha_inicio' => $session['fecha_inicio'],
                'fecha_expiracion' => $session['fecha_expiracion'],
                'recordar_sesion' => (bool)$session['recordar_sesion']
            ]
        ];

        jsonResponse($response, 200);

    } else {
        // Sesión inválida o expirada
        jsonResponse(['success' => false, 'message' => 'Sesión inválida o expirada'], 401);
    }

} catch (Exception $e) {
    error_log("Error en verificación de sesión: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Error interno del servidor'], 500);
}
?>
