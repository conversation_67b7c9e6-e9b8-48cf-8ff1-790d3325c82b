/* ========================================
   ESTILOS PARA AUTENTICACIÓN AVISOFT
   Estilo consistente con el software
   ======================================== */

/* Reset y configuración base */
.auth-body {
    margin: 0;
    padding: 0;
    font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Contenedor principal */
.auth-container {
    display: flex;
    max-width: 1000px;
    width: 90%;
    min-height: 650px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #e0e6ed;
}

/* Tarjeta de formulario */
.auth-card {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 480px;
    background: #fafbfc;
    border-right: 1px solid #e0e6ed;
}

/* Header del formulario */
.auth-header {
    text-align: center;
    margin-bottom: 35px;
}

.auth-logo img {
    width: 70px;
    height: 70px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.auth-header h1 {
    color: #2e7d32;
    font-size: 1.8rem;
    margin-bottom: 8px;
    font-weight: 600;
    letter-spacing: -0.5px;
}

.auth-header p {
    color: #6c757d;
    font-size: 0.95rem;
    margin: 0;
    font-weight: 400;
}

/* Formulario */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: #495057;
    font-weight: 500;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
}

.form-group label i {
    color: #2e7d32;
    width: 14px;
    font-size: 0.85rem;
}

.form-group input {
    padding: 10px 14px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    background: white;
    font-family: inherit;
}

.form-group input:focus {
    outline: none;
    border-color: #2e7d32;
    background: white;
    box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.1);
}

.form-group input:invalid {
    border-color: #dc3545;
}

.form-group input::placeholder {
    color: #adb5bd;
    font-size: 0.9rem;
}

/* Input de contraseña */
.password-input {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input input {
    flex: 1;
    padding-right: 50px;
}

.toggle-password {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #4CAF50;
}

/* Indicador de fortaleza de contraseña */
.password-strength {
    margin-top: 8px;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-fill.weak {
    width: 25%;
    background: #f44336;
}

.strength-fill.fair {
    width: 50%;
    background: #ff9800;
}

.strength-fill.good {
    width: 75%;
    background: #2196f3;
}

.strength-fill.strong {
    width: 100%;
    background: #4CAF50;
}

.strength-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 4px;
    display: block;
}

/* Mensajes de error */
.error-message {
    color: #f44336;
    font-size: 0.85rem;
    margin-top: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

/* Opciones del formulario */
.form-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 10px 0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
}

.checkbox-container input {
    margin-right: 10px;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 3px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
    background: #4CAF50;
    border-color: #4CAF50;
}

.checkbox-container input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: #4CAF50;
    text-decoration: none;
    font-size: 0.9rem;
    align-self: flex-end;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Botones */
.auth-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-family: inherit;
}

.auth-btn.primary {
    background: #2e7d32;
    color: white;
    border: 1px solid #2e7d32;
}

.auth-btn.primary:hover {
    background: #1b5e20;
    border-color: #1b5e20;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.2);
}

.auth-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Divisor */
.auth-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e0e0e0;
}

.auth-divider span {
    background: white;
    padding: 0 20px;
    color: #666;
    font-size: 0.9rem;
}

/* Login social */
.social-login {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.social-btn {
    padding: 12px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    color: #333;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.social-btn:hover {
    border-color: #4CAF50;
    background: #f8f9fa;
}

.social-btn.google:hover {
    border-color: #db4437;
}

.social-btn.microsoft:hover {
    border-color: #0078d4;
}

/* Footer del formulario */
.auth-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.auth-footer p {
    margin: 10px 0;
    color: #666;
    font-size: 0.9rem;
}

.auth-footer a {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Panel informativo */
.auth-info-panel {
    flex: 1;
    background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
    color: white;
    padding: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.auth-info-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.05"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.05"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.03"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.03"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.info-content {
    position: relative;
    z-index: 1;
}

.info-content h2 {
    font-size: 1.9rem;
    margin-bottom: 16px;
    font-weight: 600;
    letter-spacing: -0.5px;
}

.info-content > p {
    font-size: 1rem;
    margin-bottom: 25px;
    opacity: 0.9;
    line-height: 1.5;
    font-weight: 400;
}

.info-features {
    margin-bottom: 30px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.feature-item i {
    font-size: 1.5rem;
    margin-top: 5px;
    opacity: 0.9;
}

.feature-item h4 {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.feature-item p {
    margin: 0;
    opacity: 0.8;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Testimonial */
.testimonial {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.testimonial blockquote {
    margin: 0 0 15px 0;
    font-style: italic;
    font-size: 1rem;
    line-height: 1.5;
}

.testimonial cite {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Estadísticas */
.stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 30px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.85rem;
    opacity: 0.8;
}

/* Notificaciones */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4CAF50;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.notification.show {
    transform: translateX(0);
}

.notification.error {
    background: #f44336;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Elementos específicos del software */
.software-stats {
    display: flex;
    gap: 20px;
    margin: 25px 0;
}

.stat-box {
    text-align: center;
    flex: 1;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.8rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.access-note {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 6px;
    margin-top: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.access-note i {
    font-size: 1.1rem;
    opacity: 0.8;
}

.access-note p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.registration-benefits {
    margin-top: 25px;
}

.registration-benefits h4 {
    font-size: 1rem;
    margin-bottom: 12px;
    font-weight: 500;
}

.registration-benefits ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.registration-benefits li {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.registration-benefits i {
    color: #81c784;
    font-size: 0.8rem;
}

/* Responsive */
@media (max-width: 768px) {
    .auth-container {
        flex-direction: column;
        width: 95%;
        min-height: auto;
    }

    .auth-info-panel {
        order: -1;
        padding: 30px 20px;
    }

    .auth-card {
        padding: 30px 20px;
        border-right: none;
        border-bottom: 1px solid #e0e6ed;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .software-stats {
        justify-content: center;
        gap: 30px;
    }

    .social-login {
        flex-direction: column;
    }

    .info-content h2 {
        font-size: 1.6rem;
    }

    .feature-item {
        margin-bottom: 15px;
    }
}

@media (max-width: 480px) {
    .auth-header h1 {
        font-size: 1.5rem;
    }

    .auth-logo img {
        width: 60px;
        height: 60px;
    }

    .form-group input {
        padding: 10px 14px;
    }

    .auth-btn {
        padding: 12px 20px;
    }
}
