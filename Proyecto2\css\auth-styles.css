/* ========================================
   ESTILOS PARA AUTENTICACIÓN (LOGIN/REGISTRO)
   ======================================== */

/* Reset y configuración base */
.auth-body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Contenedor principal */
.auth-container {
    display: flex;
    max-width: 1200px;
    width: 90%;
    min-height: 600px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Tarjeta de formulario */
.auth-card {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 500px;
}

/* Header del formulario */
.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-logo img {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
}

.auth-header h1 {
    color: #2e7d32;
    font-size: 2rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.auth-header p {
    color: #666;
    font-size: 1rem;
    margin: 0;
}

/* Formulario */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: #333;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-group label i {
    color: #4CAF50;
    width: 16px;
}

.form-group input {
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-group input:focus {
    outline: none;
    border-color: #4CAF50;
    background: white;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group input:invalid {
    border-color: #f44336;
}

/* Input de contraseña */
.password-input {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input input {
    flex: 1;
    padding-right: 50px;
}

.toggle-password {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #4CAF50;
}

/* Indicador de fortaleza de contraseña */
.password-strength {
    margin-top: 8px;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-fill.weak {
    width: 25%;
    background: #f44336;
}

.strength-fill.fair {
    width: 50%;
    background: #ff9800;
}

.strength-fill.good {
    width: 75%;
    background: #2196f3;
}

.strength-fill.strong {
    width: 100%;
    background: #4CAF50;
}

.strength-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 4px;
    display: block;
}

/* Mensajes de error */
.error-message {
    color: #f44336;
    font-size: 0.85rem;
    margin-top: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

/* Opciones del formulario */
.form-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 10px 0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
}

.checkbox-container input {
    margin-right: 10px;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 3px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
    background: #4CAF50;
    border-color: #4CAF50;
}

.checkbox-container input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: #4CAF50;
    text-decoration: none;
    font-size: 0.9rem;
    align-self: flex-end;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Botones */
.auth-btn {
    padding: 14px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.auth-btn.primary {
    background: #4CAF50;
    color: white;
}

.auth-btn.primary:hover {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

.auth-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Divisor */
.auth-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e0e0e0;
}

.auth-divider span {
    background: white;
    padding: 0 20px;
    color: #666;
    font-size: 0.9rem;
}

/* Login social */
.social-login {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.social-btn {
    padding: 12px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    color: #333;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.social-btn:hover {
    border-color: #4CAF50;
    background: #f8f9fa;
}

.social-btn.google:hover {
    border-color: #db4437;
}

.social-btn.microsoft:hover {
    border-color: #0078d4;
}

/* Footer del formulario */
.auth-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.auth-footer p {
    margin: 10px 0;
    color: #666;
    font-size: 0.9rem;
}

.auth-footer a {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Panel informativo */
.auth-info-panel {
    flex: 1;
    background: linear-gradient(135deg, #2e7d32 0%, #4CAF50 100%);
    color: white;
    padding: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-content h2 {
    font-size: 2.2rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.info-content > p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
    line-height: 1.6;
}

.info-features {
    margin-bottom: 30px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.feature-item i {
    font-size: 1.5rem;
    margin-top: 5px;
    opacity: 0.9;
}

.feature-item h4 {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.feature-item p {
    margin: 0;
    opacity: 0.8;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Testimonial */
.testimonial {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.testimonial blockquote {
    margin: 0 0 15px 0;
    font-style: italic;
    font-size: 1rem;
    line-height: 1.5;
}

.testimonial cite {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Estadísticas */
.stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 30px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.85rem;
    opacity: 0.8;
}

/* Notificaciones */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4CAF50;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.notification.show {
    transform: translateX(0);
}

.notification.error {
    background: #f44336;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Responsive */
@media (max-width: 768px) {
    .auth-container {
        flex-direction: column;
        width: 95%;
        min-height: auto;
    }

    .auth-info-panel {
        order: -1;
        padding: 30px 20px;
    }

    .auth-card {
        padding: 30px 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .social-login {
        flex-direction: column;
    }

    .info-content h2 {
        font-size: 1.8rem;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .auth-header h1 {
        font-size: 1.5rem;
    }

    .auth-logo img {
        width: 60px;
        height: 60px;
    }

    .form-group input {
        padding: 10px 14px;
    }

    .auth-btn {
        padding: 12px 20px;
    }
}
