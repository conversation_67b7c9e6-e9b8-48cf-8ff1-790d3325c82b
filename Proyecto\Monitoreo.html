<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Monitoreo - AviSoft</title>
  <link rel="stylesheet" href="Styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <aside>
    <div class="logo">
      <img src="assetslogo.png.png" alt="AviSoft Logo">
    </div>
    <nav class="menu">
      <a href="index.html"><i class="fas fa-home"></i> Inicio</a>
      <a href="introduccion.html"><i class="fas fa-info-circle"></i> Introducción</a>
      <a href="monitoreo.html" class="active"><i class="fas fa-chart-line"></i> Monitoreo</a>
      <a href="#"><i class="fas fa-cog"></i> Configuración</a>
      <a href="#"><i class="fas fa-sign-out-alt"></i> Cerrar Sesión</a>
    </nav>
  </aside>

  <main>
    <div class="header">Paneles de Monitoreo </div>

    <div class="dashboard-container">
      <!-- Panel de estado del sistema -->
      <div class="dashboard-card system-status">
        <div class="card-header">
          <h3><i class="fas fa-server"></i> Estado del Sistema</h3>
        </div>
        <div class="card-body">
          <div class="status-item">
            <div class="status-label">Sensor Principal</div>
            <div class="status-value online">Conectado</div>
          </div>
          <div class="status-item">
            <div class="status-label">Conexión Wi-Fi</div>
            <div class="status-value online">Activa</div>
          </div>
          <div class="status-item">
            <div class="status-label">Última Actualización</div>
            <div class="status-value" id="last-update">Hace 0 minutos</div>
          </div>
        </div>
      </div>

      <!-- Panel de temperatura -->
      <div class="dashboard-card temperature">
        <div class="card-header">
          <h3><i class="fas fa-thermometer-half"></i> Temperatura</h3>
        </div>
        <div class="card-body">
          <div class="current-reading">
            <span class="reading-value">24.5</span>
            <span class="reading-unit">°C</span>
          </div>
          <div class="reading-chart">
            <div class="chart-placeholder">Gráfico de temperatura</div>
          </div>
          <div class="reading-stats">
            <div class="stat-item">
              <div class="stat-label">Mínima</div>
              <div class="stat-value">22.1°C</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">Máxima</div>
              <div class="stat-value">26.8°C</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">Promedio</div>
              <div class="stat-value">24.2°C</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Panel de humedad -->
      <div class="dashboard-card humidity">
        <div class="card-header">
          <h3><i class="fas fa-tint"></i> Humedad</h3>
        </div>
        <div class="card-body">
          <div class="current-reading">
            <span class="reading-value">65</span>
            <span class="reading-unit">%</span>
          </div>
          <div class="reading-chart">
            <div class="chart-placeholder">Gráfico de humedad</div>
          </div>
          <div class="reading-stats">
            <div class="stat-item">
              <div class="stat-label">Mínima</div>
              <div class="stat-value">58%</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">Máxima</div>
              <div class="stat-value">72%</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">Promedio</div>
              <div class="stat-value">64%</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Panel de historial -->
      <div class="dashboard-card history">
        <div class="card-header">
          <h3><i class="fas fa-history"></i> Historial de Registros</h3>
        </div>
        <div class="card-body">
          <table class="data-table">
            <thead>
              <tr>
                <th>Fecha</th>
                <th>Hora</th>
                <th>Temperatura</th>
                <th>Humedad</th>
                <th>Estado</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>15/05/2024</td>
                <td>14:30</td>
                <td>24.5°C</td>
                <td>65%</td>
                <td><span class="status-badge normal">Normal</span></td>
              </tr>
              <tr>
                <td>15/05/2024</td>
                <td>13:30</td>
                <td>25.2°C</td>
                <td>63%</td>
                <td><span class="status-badge normal">Normal</span></td>
              </tr>
              <tr>
                <td>15/05/2024</td>
                <td>12:30</td>
                <td>26.8°C</td>
                <td>58%</td>
                <td><span class="status-badge warning">Alerta</span></td>
              </tr>
              <tr>
                <td>15/05/2024</td>
                <td>11:30</td>
                <td>25.7°C</td>
                <td>60%</td>
                <td><span class="status-badge normal">Normal</span></td>
              </tr>
              <tr>
                <td>15/05/2024</td>
                <td>10:30</td>
                <td>24.8°C</td>
                <td>62%</td>
                <td><span class="status-badge normal">Normal</span></td>
              </tr>
            </tbody>
          </table>
          <div class="table-actions">
            <button class="action-button">Ver más registros</button>
            <button class="action-button">Exportar datos</button>
          </div>
        </div>
      </div>
    </div>

    <footer>
      &copy;2024 AVISOFT - Sistema de Monitoreo Avícola
    </footer>
  </main>

  <script>
    // Simular datos para los gráficos
    document.addEventListener('DOMContentLoaded', function() {
      // Reemplazar los placeholders con gráficos reales
      const tempChartPlaceholder = document.querySelector('.temperature .chart-placeholder');
      const humidityChartPlaceholder = document.querySelector('.humidity .chart-placeholder');

      // Crear canvas para los gráficos
      tempChartPlaceholder.innerHTML = '';
      const tempCanvas = document.createElement('canvas');
      tempCanvas.id = 'tempChart';
      tempChartPlaceholder.parentNode.replaceChild(tempCanvas, tempChartPlaceholder);

      humidityChartPlaceholder.innerHTML = '';
      const humidityCanvas = document.createElement('canvas');
      humidityCanvas.id = 'humidityChart';
      humidityChartPlaceholder.parentNode.replaceChild(humidityCanvas, humidityChartPlaceholder);

      // Datos para el gráfico de temperatura
      const tempData = {
        labels: ['10:30', '11:30', '12:30', '13:30', '14:30'],
        datasets: [{
          label: 'Temperatura (°C)',
          data: [24.8, 25.7, 26.8, 25.2, 24.5],
          borderColor: '#4CAF50',
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          borderWidth: 2,
          tension: 0.4,
          fill: true
        }]
      };

      // Datos para el gráfico de humedad
      const humidityData = {
        labels: ['10:30', '11:30', '12:30', '13:30', '14:30'],
        datasets: [{
          label: 'Humedad (%)',
          data: [62, 60, 58, 63, 65],
          borderColor: '#2196F3',
          backgroundColor: 'rgba(33, 150, 243, 0.1)',
          borderWidth: 2,
          tension: 0.4,
          fill: true
        }]
      };

      // Opciones comunes para los gráficos
      const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: false
          }
        }
      };

      // Crear gráficos
      new Chart(tempCanvas, {
        type: 'line',
        data: tempData,
        options: chartOptions
      });

      new Chart(humidityCanvas, {
        type: 'line',
        data: humidityData,
        options: chartOptions
      });

      // Simular actualización de datos en tiempo real
      let updateCounter = 0;
      const updateInterval = 60; // Actualizar cada 60 segundos (1 minuto)
      const lastUpdateElement = document.getElementById('last-update');

      // Función para actualizar el contador de tiempo
      function updateTimeCounter() {
        updateCounter++;
        if (updateCounter >= updateInterval) {
          updateCounter = 0;
          updateSensorData();
        }

        // Actualizar el texto de última actualización
        const minutesSinceUpdate = Math.floor(updateCounter / 60);
        const secondsSinceUpdate = updateCounter % 60;

        if (minutesSinceUpdate === 0) {
          lastUpdateElement.textContent = `Hace ${secondsSinceUpdate} segundos`;
        } else {
          lastUpdateElement.textContent = `Hace ${minutesSinceUpdate} min ${secondsSinceUpdate} seg`;
        }
      }

      // Función para actualizar los datos de los sensores
      function updateSensorData() {
        const tempValue = document.querySelector('.temperature .reading-value');
        const humidityValue = document.querySelector('.humidity .reading-value');

        // Generar valores aleatorios cercanos a los actuales
        const newTemp = (parseFloat(tempValue.textContent) + (Math.random() * 0.4 - 0.2)).toFixed(1);
        const newHumidity = Math.round(parseFloat(humidityValue.textContent) + (Math.random() * 2 - 1));

        // Actualizar valores
        tempValue.textContent = newTemp;
        humidityValue.textContent = newHumidity;

        // Actualizar la tabla de historial con los nuevos datos
        updateHistoryTable(newTemp, newHumidity);

        // Mostrar notificación de actualización
        showUpdateNotification();
      }

      // Función para actualizar la tabla de historial
      function updateHistoryTable(temp, humidity) {
        const tableBody = document.querySelector('.data-table tbody');
        const now = new Date();
        const dateStr = now.toLocaleDateString('es-ES');
        const timeStr = now.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' });

        // Crear nueva fila
        const newRow = document.createElement('tr');
        newRow.innerHTML = `
          <td>${dateStr}</td>
          <td>${timeStr}</td>
          <td>${temp}°C</td>
          <td>${humidity}%</td>
          <td><span class="status-badge ${parseFloat(temp) > 26 ? 'warning' : 'normal'}">${parseFloat(temp) > 26 ? 'Alerta' : 'Normal'}</span></td>
        `;

        // Insertar al principio de la tabla
        tableBody.insertBefore(newRow, tableBody.firstChild);

        // Limitar a 5 filas
        if (tableBody.children.length > 5) {
          tableBody.removeChild(tableBody.lastChild);
        }
      }

      // Función para mostrar notificación de actualización
      function showUpdateNotification() {
        const notification = document.createElement('div');
        notification.className = 'update-notification';
        notification.textContent = 'Datos actualizados';
        document.body.appendChild(notification);

        // Eliminar la notificación después de 3 segundos
        setTimeout(() => {
          notification.classList.add('fade-out');
          setTimeout(() => {
            document.body.removeChild(notification);
          }, 500);
        }, 3000);
      }

      // Iniciar el contador de tiempo (actualiza cada segundo)
      setInterval(updateTimeCounter, 1000);

      // Realizar la primera actualización inmediatamente
      updateSensorData();

      // Hacer que los botones de acción sean interactivos
      const actionButtons = document.querySelectorAll('.action-button');
      actionButtons.forEach(button => {
        button.addEventListener('click', function() {
          alert('Función: ' + this.textContent + ' - Esta funcionalidad estará disponible próximamente.');
        });
      });
    });
  </script>
</body>
</html>
