/* ========================================
   ESTILOS PARA BÚSQUEDA INTELIGENTE
   ======================================== */

/* Contenedor de resultados de búsqueda */
.search-results-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
  margin-top: 5px;
}

/* Elemento individual de resultado */
.search-result-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: #f8f9fa;
  transform: translateX(5px);
}

.search-result-item:hover .result-arrow {
  color: #4CAF50;
  transform: translateX(5px);
}

/* Icono del resultado */
.result-icon {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.result-icon i {
  font-size: 1.2rem;
  color: #4CAF50;
}

.search-result-item:hover .result-icon {
  background: linear-gradient(135deg, #4CAF50, #66bb6a);
  transform: scale(1.1);
}

.search-result-item:hover .result-icon i {
  color: white;
}

/* Contenido del resultado */
.result-content {
  flex: 1;
  min-width: 0;
}

.result-content h4 {
  margin: 0 0 5px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.result-content p {
  margin: 0 0 5px 0;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.result-type {
  font-size: 0.8rem;
  color: #4CAF50;
  font-weight: 500;
  text-transform: capitalize;
}

/* Flecha del resultado */
.result-arrow {
  margin-left: 10px;
  color: #ccc;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

/* Estado sin resultados */
.no-results {
  text-align: center;
  padding: 30px 20px;
  color: #666;
}

.no-results i {
  font-size: 2.5rem;
  color: #ddd;
  margin-bottom: 15px;
  display: block;
}

.no-results p {
  margin: 0 0 10px 0;
  font-size: 1rem;
  font-weight: 500;
}

.no-results small {
  font-size: 0.85rem;
  color: #999;
  line-height: 1.4;
}

/* Mejoras específicas para el contenedor de búsqueda (sin sobrescribir estilos originales) */
/* Solo aplicar estilos específicos que no conflicten con el diseño original */

/* Animaciones */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-results-dropdown[style*="block"] {
  animation: slideDown 0.3s ease;
}

/* Scrollbar personalizada para resultados */
.search-results-dropdown::-webkit-scrollbar {
  width: 6px;
}

.search-results-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.search-results-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.search-results-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive para búsqueda */
@media (max-width: 768px) {
  .search-results-dropdown {
    max-height: 300px;
    border-radius: 8px;
  }

  .search-result-item {
    padding: 12px;
  }

  .result-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }

  .result-icon i {
    font-size: 1.1rem;
  }

  .result-content h4 {
    font-size: 0.95rem;
  }

  .result-content p {
    font-size: 0.85rem;
  }

  .result-type {
    font-size: 0.75rem;
  }

  .no-results {
    padding: 20px 15px;
  }

  .no-results i {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {

  .search-results-dropdown {
    max-height: 250px;
  }

  .search-result-item {
    padding: 10px;
  }

  .result-icon {
    width: 35px;
    height: 35px;
    margin-right: 10px;
  }

  .result-icon i {
    font-size: 1rem;
  }

  .result-content h4 {
    font-size: 0.9rem;
  }

  .result-content p {
    font-size: 0.8rem;
    -webkit-line-clamp: 1;
  }
}

/* Estados especiales */
.search-result-item.highlighted {
  background-color: #e8f5e9;
  border-left: 4px solid #4CAF50;
}

.search-result-item.highlighted .result-icon {
  background: linear-gradient(135deg, #4CAF50, #66bb6a);
}

.search-result-item.highlighted .result-icon i {
  color: white;
}

/* Estilos para respuestas directas */
.search-result-item.direct-answer {
  background: linear-gradient(135deg, #fff3e0, #ffe0b2) !important;
  border: 2px solid #ff9800 !important;
  border-radius: 12px !important;
  margin-bottom: 10px !important;
  position: relative !important;
  padding: 20px !important;
}

.search-result-item.direct-answer::before {
  content: "💡 RESPUESTA DIRECTA";
  position: absolute;
  top: -10px;
  left: 20px;
  background: #ff9800;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: bold;
  letter-spacing: 0.5px;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.search-result-item.direct-answer:hover {
  background: linear-gradient(135deg, #fff8e1, #ffecb3) !important;
  transform: translateX(3px) !important;
  box-shadow: 0 8px 25px rgba(255, 152, 0, 0.2) !important;
}

.search-result-item.direct-answer .result-icon {
  background: linear-gradient(135deg, #ff9800, #f57c00) !important;
  border: none !important;
}

.search-result-item.direct-answer .result-icon i {
  color: white !important;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.search-result-item.direct-answer .result-content h4.direct-question {
  color: #e65100 !important;
  font-weight: 700 !important;
  font-size: 1.05rem !important;
  margin-bottom: 12px !important;
  line-height: 1.3 !important;
}

.search-result-item.direct-answer .result-content p.direct-response {
  color: #333 !important;
  font-size: 0.95rem !important;
  line-height: 1.6 !important;
  background: rgba(255, 255, 255, 0.8) !important;
  padding: 12px !important;
  border-radius: 8px !important;
  border-left: 4px solid #ff9800 !important;
  margin: 10px 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.search-result-item.direct-answer .result-content small.result-type {
  color: #ff9800 !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  font-size: 0.8rem !important;
  background: rgba(255, 152, 0, 0.1) !important;
  padding: 2px 8px !important;
  border-radius: 10px !important;
}

.search-result-item.direct-answer .result-arrow {
  color: #ff9800 !important;
  font-size: 1.1rem !important;
}

.search-result-item.direct-answer:hover .result-arrow {
  color: #e65100 !important;
  transform: translateX(8px) !important;
}

/* Responsive para respuestas directas */
@media (max-width: 768px) {
  .search-result-item.direct-answer {
    padding: 15px !important;
    margin-bottom: 8px !important;
  }

  .search-result-item.direct-answer::before {
    top: -8px;
    left: 15px;
    padding: 3px 10px;
    font-size: 0.7rem;
  }

  .search-result-item.direct-answer .result-content h4.direct-question {
    font-size: 1rem !important;
    margin-bottom: 10px !important;
  }

  .search-result-item.direct-answer .result-content p.direct-response {
    font-size: 0.9rem !important;
    padding: 10px !important;
    margin: 8px 0 !important;
  }

  .search-result-item.direct-answer .result-content small.result-type {
    font-size: 0.75rem !important;
    padding: 1px 6px !important;
  }
}

@media (max-width: 480px) {
  .search-result-item.direct-answer {
    padding: 12px !important;
    margin-bottom: 6px !important;
  }

  .search-result-item.direct-answer::before {
    top: -6px;
    left: 12px;
    padding: 2px 8px;
    font-size: 0.65rem;
  }

  .search-result-item.direct-answer .result-content h4.direct-question {
    font-size: 0.95rem !important;
    margin-bottom: 8px !important;
  }

  .search-result-item.direct-answer .result-content p.direct-response {
    font-size: 0.85rem !important;
    padding: 8px !important;
    margin: 6px 0 !important;
    line-height: 1.5 !important;
  }

  .search-result-item.direct-answer .result-content small.result-type {
    font-size: 0.7rem !important;
    padding: 1px 5px !important;
  }

  .search-result-item.direct-answer .result-arrow {
    font-size: 1rem !important;
  }
}

/* Indicador de carga */
.search-loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.search-loading i {
  font-size: 1.5rem;
  color: #4CAF50;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Sugerencias de búsqueda */
.search-suggestions {
  padding: 15px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.search-suggestions h5 {
  margin: 0 0 10px 0;
  font-size: 0.85rem;
  color: #666;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.suggestion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-tag {
  background: #e8f5e9;
  color: #2e7d32;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.suggestion-tag:hover {
  background: #4CAF50;
  color: white;
  transform: translateY(-1px);
}

/* ========================================
   ESTILOS PARA EJEMPLOS DE BÚSQUEDA
   ======================================== */

.search-examples {
  background: linear-gradient(135deg, #f8f9fa, #e8f5e9);
  border-radius: 15px;
  padding: 30px;
  margin: 30px 0;
  border-left: 5px solid #4CAF50;
}

.search-examples h3 {
  color: #2e7d32;
  font-size: 1.4rem;
  margin-bottom: 25px;
  text-align: center;
  font-weight: 600;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.example-category {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.example-category:hover {
  transform: translateY(-5px);
}

.example-category h4 {
  color: #2e7d32;
  font-size: 1.1rem;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  font-weight: 600;
}

.example-category h4 i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.example-category ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.example-category li {
  background: #f8f9fa;
  margin-bottom: 8px;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #555;
}

.example-category li:hover {
  background: #e8f5e9;
  border-left-color: #4CAF50;
  transform: translateX(5px);
  color: #2e7d32;
}

.example-category li:last-child {
  margin-bottom: 0;
}

/* Responsive para ejemplos */
@media (max-width: 768px) {
  .search-examples {
    padding: 20px;
    margin: 20px 0;
  }

  .search-examples h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }

  .examples-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .example-category {
    padding: 15px;
  }

  .example-category h4 {
    font-size: 1rem;
  }

  .example-category li {
    padding: 8px 12px;
    font-size: 0.85rem;
  }
}

/* ========================================
   ESTILOS PARA PÁGINA DE PRUEBA AMPLIADA
   ======================================== */

/* Instrucciones de prueba */
.test-instructions {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin: 30px 0;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border-left: 5px solid #2196F3;
}

.test-instructions h3 {
  color: #1976D2;
  font-size: 1.4rem;
  margin-bottom: 25px;
  text-align: center;
  font-weight: 600;
}

.instruction-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.step {
  display: flex;
  align-items: flex-start;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.step:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.step-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  margin-right: 15px;
  flex-shrink: 0;
}

.step-content h4 {
  color: #1976D2;
  font-size: 1.1rem;
  margin-bottom: 8px;
  font-weight: 600;
}

.step-content p {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

/* Estadísticas de la base de datos */
.database-stats {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-radius: 15px;
  padding: 30px;
  margin: 30px 0;
  border-left: 5px solid #2196F3;
}

.database-stats h3 {
  color: #1976D2;
  font-size: 1.4rem;
  margin-bottom: 25px;
  text-align: center;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
}

.stat-icon i {
  font-size: 1.8rem;
  color: #1976D2;
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 700;
  color: #1976D2;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 1rem;
  color: #555;
  font-weight: 500;
}

/* Responsive para nuevos elementos */
@media (max-width: 768px) {
  .test-instructions {
    padding: 20px;
    margin: 20px 0;
  }

  .test-instructions h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }

  .instruction-steps {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .step {
    padding: 15px;
  }

  .step-number {
    width: 35px;
    height: 35px;
    font-size: 1rem;
    margin-right: 12px;
  }

  .step-content h4 {
    font-size: 1rem;
  }

  .step-content p {
    font-size: 0.9rem;
  }

  .database-stats {
    padding: 20px;
    margin: 20px 0;
  }

  .database-stats h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 12px;
  }

  .stat-icon i {
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 1.8rem;
  }

  .stat-label {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-number {
    font-size: 1.6rem;
  }
}

/* ========================================
   ESTILOS PARA VERIFICACIÓN DE DISEÑO
   ======================================== */

.comparison-section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin: 30px 0;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border-left: 5px solid #4CAF50;
}

.comparison-section h3 {
  color: #2e7d32;
  font-size: 1.4rem;
  margin-bottom: 25px;
  text-align: center;
  font-weight: 600;
}

.verification-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.verification-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  position: relative;
  transition: transform 0.3s ease;
}

.verification-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.verification-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
}

.verification-icon i {
  font-size: 1.5rem;
  color: #2e7d32;
}

.verification-item h4 {
  color: #2e7d32;
  font-size: 1.1rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.verification-item p {
  color: #555;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 10px;
}

.status-check {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 1.2rem;
  color: #4CAF50;
}

.technical-specs {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-radius: 15px;
  padding: 30px;
  margin: 30px 0;
  border-left: 5px solid #2196F3;
}

.technical-specs h3 {
  color: #1976D2;
  font-size: 1.4rem;
  margin-bottom: 25px;
  text-align: center;
  font-weight: 600;
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.spec-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.spec-item h4 {
  color: #1976D2;
  font-size: 1.1rem;
  margin-bottom: 15px;
  font-weight: 600;
  border-bottom: 2px solid #e3f2fd;
  padding-bottom: 8px;
}

.spec-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.spec-item li {
  background: #f8f9fa;
  margin-bottom: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: #555;
  border-left: 3px solid #2196F3;
}

.spec-item li:last-child {
  margin-bottom: 0;
}

/* Responsive para verificación */
@media (max-width: 768px) {
  .comparison-section {
    padding: 20px;
    margin: 20px 0;
  }

  .verification-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .verification-item {
    padding: 15px;
  }

  .verification-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 12px;
  }

  .verification-icon i {
    font-size: 1.2rem;
  }

  .technical-specs {
    padding: 20px;
    margin: 20px 0;
  }

  .specs-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .spec-item {
    padding: 15px;
  }

  .spec-item h4 {
    font-size: 1rem;
  }

  .spec-item li {
    font-size: 0.8rem;
    padding: 6px 10px;
  }
}
