# 🚀 Guía Completa: Instalar PHP para AviSoft

## 🎯 **Opción 1: XAMPP (Recomendada para Principiantes)**

### **📥 Paso 1: Descargar XAMPP**
1. **Abrir navegador** y ir a: https://www.apachefriends.org/
2. **Hacer clic** en el botón grande "Download" 
3. **Seleccionar** "XAMPP for Windows"
4. **<PERSON>cargar** (archivo de ~150MB)

### **💿 Paso 2: Instalar XAMPP**
1. **Buscar** el archivo descargado (normalmente en Descargas)
2. **Hacer clic derecho** → "Ejecutar como administrador"
3. **Si aparece advertencia de antivirus:** Hacer clic "Yes"
4. **Pantalla de bienvenida:** Hacer clic "Next"
5. **Seleccionar componentes:**
   ```
   ✅ Apache          (Servidor web)
   ✅ MySQL           (Base de datos)
   ✅ PHP             (Lenguaje)
   ✅ phpMyAdmin      (Administrar BD)
   ❌ Mercury         (No necesario)
   ❌ Tomcat          (No necesario)
   ❌ Perl            (No necesario)
   ```
6. **Carpeta de instalación:** Dejar `C:\xampp` (recomendado)
7. **Bitnami:** Desmarcar (no necesario)
8. **Hacer clic** "Next" hasta completar instalación

### **🚀 Paso 3: Iniciar XAMPP**
1. **Buscar** "XAMPP Control Panel" en el menú inicio
2. **Ejecutar como administrador**
3. **En el panel de control:**
   - **Apache:** Hacer clic "Start" (debe ponerse verde)
   - **MySQL:** Hacer clic "Start" (debe ponerse verde)
4. **Si aparece firewall:** Permitir acceso

### **📁 Paso 4: Mover tu Proyecto**
1. **Abrir** Explorador de archivos
2. **Navegar** a `C:\xampp\htdocs\`
3. **Copiar** toda la carpeta `Proyecto2`
4. **Pegar** dentro de `htdocs`
5. **Resultado:** `C:\xampp\htdocs\Proyecto2\`

### **🌐 Paso 5: Probar**
1. **Abrir navegador**
2. **Ir a:** `http://localhost/Proyecto2/test-api.html`
3. **Hacer clic** "1. Probar PHP"
4. **Debería mostrar:** ✅ PHP funcionando

---

## 🎯 **Opción 2: Solo PHP (Para Usuarios Avanzados)**

### **📥 Paso 1: Descargar PHP**
1. **Ir a:** https://windows.php.net/download/
2. **Buscar** "Thread Safe" en la tabla
3. **Hacer clic** en "Zip" de la versión más reciente
4. **Descargar** (archivo de ~30MB)

### **📂 Paso 2: Extraer PHP**
1. **Crear carpeta** `C:\php`
2. **Extraer** el ZIP descargado en `C:\php`
3. **Verificar** que existe `C:\php\php.exe`

### **⚙️ Paso 3: Configurar PATH**
1. **Presionar** `Windows + X`
2. **Seleccionar** "Sistema"
3. **Hacer clic** "Configuración avanzada del sistema"
4. **Hacer clic** "Variables de entorno"
5. **En "Variables del sistema":**
   - **Buscar** "Path"
   - **Hacer clic** "Editar"
   - **Hacer clic** "Nuevo"
   - **Escribir** `C:\php`
   - **Hacer clic** "Aceptar" (en todas las ventanas)

### **✅ Paso 4: Verificar**
1. **Presionar** `Windows + R`
2. **Escribir** `cmd` y presionar Enter
3. **Escribir** `php --version`
4. **Debería mostrar:** Información de PHP

### **🚀 Paso 5: Usar**
1. **En CMD, navegar** a tu proyecto:
   ```cmd
   cd ruta\a\tu\Proyecto2
   ```
2. **Iniciar servidor:**
   ```cmd
   php -S localhost:8000
   ```
3. **Abrir navegador:** `http://localhost:8000/registro.html`

---

## 🆘 **Solución de Problemas**

### **❌ "Apache no inicia" (XAMPP)**
**Causa:** Puerto 80 ocupado
**Solución:**
1. **En XAMPP Control Panel** → Config (Apache) → httpd.conf
2. **Cambiar** `Listen 80` por `Listen 8080`
3. **Reiniciar** Apache
4. **Acceder:** `http://localhost:8080/Proyecto2/`

### **❌ "MySQL no inicia" (XAMPP)**
**Causa:** Puerto 3306 ocupado
**Solución:**
1. **En XAMPP Control Panel** → Config (MySQL) → my.ini
2. **Cambiar** `port=3306` por `port=3307`
3. **Actualizar** `config/database.php`:
   ```php
   define('DB_HOST', 'localhost:3307');
   ```

### **❌ "php no se reconoce" (Solo PHP)**
**Causa:** PATH no configurado
**Solución:**
1. **Verificar** que `C:\php\php.exe` existe
2. **Repetir** configuración de PATH
3. **Reiniciar** CMD

### **❌ "Error de permisos"**
**Solución:**
1. **Ejecutar** XAMPP como administrador
2. **O mover** proyecto a carpeta sin permisos especiales

---

## 🎯 **¿Cuál Opción Elegir?**

### **✅ Elige XAMPP si:**
- ✅ Eres principiante
- ✅ Quieres todo en uno
- ✅ Planeas usar base de datos
- ✅ Prefieres interfaz gráfica

### **✅ Elige Solo PHP si:**
- ✅ Eres usuario avanzado
- ✅ Ya tienes MySQL instalado
- ✅ Quieres instalación mínima
- ✅ Prefieres línea de comandos

---

## 🎉 **Una vez instalado:**

1. **Tu proyecto funcionará** en:
   - **XAMPP:** `http://localhost/Proyecto2/`
   - **Solo PHP:** `http://localhost:8000/`

2. **El registro guardará** datos en la base de datos

3. **No más errores** de "respuesta del servidor"

¿Necesitas ayuda con algún paso específico?
